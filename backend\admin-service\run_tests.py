#!/usr/bin/env python3
"""
Test runner script for the admin service
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command: list, description: str) -> bool:
    """Run a command and return success status."""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(command)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False


def main():
    """Main test runner function."""
    print("🚀 Starting Admin Service Test Suite")
    
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Test commands to run
    test_commands = [
        {
            "command": ["python", "-m", "pytest", "--version"],
            "description": "Check pytest installation"
        },
        {
            "command": ["python", "-m", "pytest", "tests/", "-v", "--tb=short"],
            "description": "Run all tests with verbose output"
        },
        {
            "command": ["python", "-m", "pytest", "tests/", "--cov=app", "--cov-report=term-missing"],
            "description": "Run tests with coverage report"
        },
        {
            "command": ["python", "-m", "pytest", "tests/test_auth.py", "-v"],
            "description": "Run authentication tests"
        },
        {
            "command": ["python", "-m", "pytest", "tests/test_users.py", "-v"],
            "description": "Run user management tests"
        },
        {
            "command": ["python", "-m", "pytest", "tests/test_payments.py", "-v"],
            "description": "Run payment tests"
        },
        {
            "command": ["python", "-m", "pytest", "tests/test_financial.py", "-v"],
            "description": "Run financial tests"
        },
        {
            "command": ["python", "-m", "pytest", "tests/test_audit.py", "-v"],
            "description": "Run audit tests"
        }
    ]
    
    # Run tests
    passed = 0
    failed = 0
    
    for test in test_commands:
        if run_command(test["command"], test["description"]):
            passed += 1
        else:
            failed += 1
    
    # Summary
    print(f"\n{'='*60}")
    print("🏁 TEST SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Total: {passed + failed}")
    
    if failed > 0:
        print("\n⚠️  Some tests failed. Please check the output above.")
        sys.exit(1)
    else:
        print("\n🎉 All tests passed successfully!")
        sys.exit(0)


if __name__ == "__main__":
    main()
