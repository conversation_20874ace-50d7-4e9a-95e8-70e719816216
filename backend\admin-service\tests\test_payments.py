"""
Payment management tests
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from decimal import Decimal

from app.core.config import settings


class TestPaymentManagement:
    """Test payment management endpoints."""

    def test_create_payment(self, client: TestClient, admin_auth_headers):
        """Test creating a new payment record."""
        payment_data = {
            "student_name": "<PERSON>",
            "student_email": "<EMAIL>",
            "amount": 100.00,
            "payment_method": "card",
            "description": "Course fee payment",
            "course_name": "Python Programming"
        }
        response = client.post(
            f"{settings.API_V1_STR}/payments/",
            json=payment_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 201
        data = response.json()
        assert data["student_name"] == payment_data["student_name"]
        assert float(data["amount"]) == payment_data["amount"]
        assert data["status"] == "pending"

    def test_get_payments_list(self, client: TestClient, admin_auth_headers):
        """Test getting list of payments."""
        response = client.get(
            f"{settings.API_V1_STR}/payments/",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_get_payment_by_id(self, client: TestClient, admin_auth_headers):
        """Test getting payment by ID."""
        # First create a payment
        payment_data = {
            "student_name": "Jane Doe",
            "student_email": "<EMAIL>",
            "amount": 150.00,
            "payment_method": "cash",
            "description": "Workshop fee"
        }
        create_response = client.post(
            f"{settings.API_V1_STR}/payments/",
            json=payment_data,
            headers=admin_auth_headers
        )
        payment_id = create_response.json()["id"]

        # Get the payment
        response = client.get(
            f"{settings.API_V1_STR}/payments/{payment_id}",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == payment_id

    def test_process_payment(self, client: TestClient, admin_auth_headers):
        """Test processing a payment."""
        # Create a payment first
        payment_data = {
            "student_name": "Bob Smith",
            "student_email": "<EMAIL>",
            "amount": 200.00,
            "payment_method": "bank_transfer",
            "description": "Course enrollment"
        }
        create_response = client.post(
            f"{settings.API_V1_STR}/payments/",
            json=payment_data,
            headers=admin_auth_headers
        )
        payment_id = create_response.json()["id"]

        # Process the payment
        response = client.post(
            f"{settings.API_V1_STR}/payments/{payment_id}/process",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "completed"

    def test_refund_payment(self, client: TestClient, admin_auth_headers):
        """Test refunding a payment."""
        # Create and process a payment first
        payment_data = {
            "student_name": "Alice Johnson",
            "student_email": "<EMAIL>",
            "amount": 300.00,
            "payment_method": "card",
            "description": "Advanced course"
        }
        create_response = client.post(
            f"{settings.API_V1_STR}/payments/",
            json=payment_data,
            headers=admin_auth_headers
        )
        payment_id = create_response.json()["id"]

        # Process the payment
        client.post(
            f"{settings.API_V1_STR}/payments/{payment_id}/process",
            headers=admin_auth_headers
        )

        # Refund the payment
        refund_data = {
            "amount": 150.00,
            "reason": "Partial refund requested"
        }
        response = client.post(
            f"{settings.API_V1_STR}/payments/{payment_id}/refund",
            json=refund_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "refunded"

    def test_get_payment_statistics(self, client: TestClient, admin_auth_headers):
        """Test getting payment statistics."""
        response = client.get(
            f"{settings.API_V1_STR}/payments/stats/summary",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert "total_payments" in data
        assert "total_amount" in data
        assert "completed_payments" in data

    def test_filter_payments_by_status(self, client: TestClient, admin_auth_headers):
        """Test filtering payments by status."""
        response = client.get(
            f"{settings.API_V1_STR}/payments/?status=pending",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_filter_payments_by_date_range(self, client: TestClient, admin_auth_headers):
        """Test filtering payments by date range."""
        response = client.get(
            f"{settings.API_V1_STR}/payments/?start_date=2024-01-01&end_date=2024-12-31",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_unauthorized_payment_access(self, client: TestClient, auth_headers):
        """Test unauthorized access to payment endpoints."""
        response = client.get(
            f"{settings.API_V1_STR}/payments/",
            headers=auth_headers
        )
        # Should return 403 if user doesn't have payment access
        assert response.status_code in [403, 200]  # Depends on implementation
