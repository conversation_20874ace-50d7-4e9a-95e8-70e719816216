"""
Authentication tests
"""

import pytest
from fastapi.testclient import Test<PERSON>lient

from app.core.config import settings


class TestAuthentication:
    """Test authentication endpoints."""

    def test_login_success(self, client: TestClient, test_user):
        """Test successful login."""
        response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": "testpassword"
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"

    def test_login_invalid_credentials(self, client: TestClient, test_user):
        """Test login with invalid credentials."""
        response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": "wrongpassword"
            }
        )
        assert response.status_code == 401

    def test_login_nonexistent_user(self, client: TestClient):
        """Test login with nonexistent user."""
        response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": "<EMAIL>",
                "password": "password"
            }
        )
        assert response.status_code == 401

    def test_get_current_user(self, client: TestClient, auth_headers, test_user):
        """Test getting current user information."""
        response = client.get(
            f"{settings.API_V1_STR}/users/me",
            headers=auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == test_user.email
        assert data["username"] == test_user.username

    def test_get_current_user_unauthorized(self, client: TestClient):
        """Test getting current user without authentication."""
        response = client.get(f"{settings.API_V1_STR}/users/me")
        assert response.status_code == 401

    def test_get_current_user_invalid_token(self, client: TestClient):
        """Test getting current user with invalid token."""
        response = client.get(
            f"{settings.API_V1_STR}/users/me",
            headers={"Authorization": "Bearer invalid_token"}
        )
        assert response.status_code == 401


class TestTokenValidation:
    """Test token validation and security."""

    def test_token_expiration(self, client: TestClient, test_user):
        """Test token expiration handling."""
        # This would require mocking time or using a very short expiration
        # For now, we'll test the basic token structure
        response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": "testpassword"
            }
        )
        assert response.status_code == 200
        token = response.json()["access_token"]
        assert isinstance(token, str)
        assert len(token) > 0

    def test_malformed_token(self, client: TestClient):
        """Test handling of malformed tokens."""
        response = client.get(
            f"{settings.API_V1_STR}/users/me",
            headers={"Authorization": "Bearer malformed.token.here"}
        )
        assert response.status_code == 401

    def test_missing_bearer_prefix(self, client: TestClient, test_user):
        """Test handling of token without Bearer prefix."""
        login_response = client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": "testpassword"
            }
        )
        token = login_response.json()["access_token"]
        
        response = client.get(
            f"{settings.API_V1_STR}/users/me",
            headers={"Authorization": token}  # Missing "Bearer " prefix
        )
        assert response.status_code == 401
