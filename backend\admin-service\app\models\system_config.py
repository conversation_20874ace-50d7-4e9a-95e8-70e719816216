"""
System configuration model
"""

from datetime import datetime
from typing import TYPE_CHECKING

from sqlalchemy import <PERSON>olean, Column, DateTime, Integer, String, Text, JSON, ForeignKey
from sqlalchemy.orm import relationship

from app.db.base_class import Base

if TYPE_CHECKING:
    from .user import User  # noqa: F401


class SystemConfig(Base):
    __tablename__ = "system_configs"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Configuration identification
    key = Column(String(100), unique=True, index=True, nullable=False)
    category = Column(String(50), nullable=False, index=True)
    
    # Configuration values
    value = Column(JSON, nullable=True)
    default_value = Column(JSON, nullable=True)
    
    # Metadata
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    data_type = Column(String(20), nullable=False)  # string, number, boolean, json, array
    
    # Validation
    validation_rules = Column(JSON, nullable=True)  # min, max, required, pattern, etc.
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_system = Column(Boolean, default=False, nullable=False)  # System configs cannot be deleted
    is_sensitive = Column(Boolean, default=False, nullable=False)  # Hide value in UI
    
    # Access control
    required_permission = Column(String(100), nullable=True)  # Permission needed to modify
    
    # Audit trail
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    updated_by_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    created_by = relationship("User", foreign_keys=[created_by_id], backref="created_configs")
    updated_by = relationship("User", foreign_keys=[updated_by_id], backref="updated_configs")
    
    def get_typed_value(self):
        """Get value with proper type conversion"""
        if self.value is None:
            return self.default_value
        
        if self.data_type == "boolean":
            return bool(self.value)
        elif self.data_type == "number":
            return float(self.value) if isinstance(self.value, (int, float, str)) else self.value
        elif self.data_type == "string":
            return str(self.value) if self.value is not None else ""
        else:
            return self.value
    
    def __repr__(self) -> str:
        return f"<SystemConfig(key='{self.key}', category='{self.category}')>"


class ConfigCategory:
    """System configuration categories"""
    GENERAL = "general"
    SECURITY = "security"
    EMAIL = "email"
    PAYMENT = "payment"
    NOTIFICATION = "notification"
    APPEARANCE = "appearance"
    INTEGRATION = "integration"
    BACKUP = "backup"
    LOGGING = "logging"


class ConfigDataType:
    """Configuration data types"""
    STRING = "string"
    NUMBER = "number"
    BOOLEAN = "boolean"
    JSON = "json"
    ARRAY = "array"


class DefaultConfigs:
    """Default system configurations"""
    
    @staticmethod
    def get_default_configs():
        """Get list of default system configurations"""
        return [
            {
                "key": "app.name",
                "category": ConfigCategory.GENERAL,
                "name": "Application Name",
                "description": "Name of the application displayed in UI",
                "data_type": ConfigDataType.STRING,
                "default_value": "Innovative Centre Platform",
                "is_system": True,
                "required_permission": "system:config:update"
            },
            {
                "key": "app.version",
                "category": ConfigCategory.GENERAL,
                "name": "Application Version",
                "description": "Current version of the application",
                "data_type": ConfigDataType.STRING,
                "default_value": "1.0.0",
                "is_system": True,
                "required_permission": "system:config:update"
            },
            {
                "key": "app.timezone",
                "category": ConfigCategory.GENERAL,
                "name": "Default Timezone",
                "description": "Default timezone for the application",
                "data_type": ConfigDataType.STRING,
                "default_value": "UTC",
                "validation_rules": {"required": True},
                "required_permission": "system:config:update"
            },
            {
                "key": "security.session_timeout",
                "category": ConfigCategory.SECURITY,
                "name": "Session Timeout (minutes)",
                "description": "User session timeout in minutes",
                "data_type": ConfigDataType.NUMBER,
                "default_value": 30,
                "validation_rules": {"min": 5, "max": 1440},
                "required_permission": "system:config:update"
            },
            {
                "key": "security.password_min_length",
                "category": ConfigCategory.SECURITY,
                "name": "Minimum Password Length",
                "description": "Minimum required password length",
                "data_type": ConfigDataType.NUMBER,
                "default_value": 8,
                "validation_rules": {"min": 6, "max": 50},
                "required_permission": "system:config:update"
            },
            {
                "key": "security.max_login_attempts",
                "category": ConfigCategory.SECURITY,
                "name": "Maximum Login Attempts",
                "description": "Maximum failed login attempts before account lockout",
                "data_type": ConfigDataType.NUMBER,
                "default_value": 5,
                "validation_rules": {"min": 3, "max": 20},
                "required_permission": "system:config:update"
            },
            {
                "key": "email.smtp_enabled",
                "category": ConfigCategory.EMAIL,
                "name": "Enable Email",
                "description": "Enable email notifications",
                "data_type": ConfigDataType.BOOLEAN,
                "default_value": False,
                "required_permission": "system:config:update"
            },
            {
                "key": "email.from_address",
                "category": ConfigCategory.EMAIL,
                "name": "From Email Address",
                "description": "Default sender email address",
                "data_type": ConfigDataType.STRING,
                "default_value": "<EMAIL>",
                "validation_rules": {"pattern": r"^[^@]+@[^@]+\.[^@]+$"},
                "required_permission": "system:config:update"
            },
            {
                "key": "payment.default_currency",
                "category": ConfigCategory.PAYMENT,
                "name": "Default Currency",
                "description": "Default currency for payments",
                "data_type": ConfigDataType.STRING,
                "default_value": "USD",
                "validation_rules": {"required": True, "pattern": r"^[A-Z]{3}$"},
                "required_permission": "system:config:update"
            },
            {
                "key": "payment.tax_rate",
                "category": ConfigCategory.PAYMENT,
                "name": "Default Tax Rate (%)",
                "description": "Default tax rate percentage",
                "data_type": ConfigDataType.NUMBER,
                "default_value": 0,
                "validation_rules": {"min": 0, "max": 100},
                "required_permission": "system:config:update"
            },
            {
                "key": "notification.email_enabled",
                "category": ConfigCategory.NOTIFICATION,
                "name": "Email Notifications",
                "description": "Enable email notifications",
                "data_type": ConfigDataType.BOOLEAN,
                "default_value": True,
                "required_permission": "system:config:update"
            },
            {
                "key": "backup.auto_backup",
                "category": ConfigCategory.BACKUP,
                "name": "Automatic Backup",
                "description": "Enable automatic database backups",
                "data_type": ConfigDataType.BOOLEAN,
                "default_value": True,
                "required_permission": "system:config:update"
            },
            {
                "key": "backup.retention_days",
                "category": ConfigCategory.BACKUP,
                "name": "Backup Retention (days)",
                "description": "Number of days to keep backup files",
                "data_type": ConfigDataType.NUMBER,
                "default_value": 30,
                "validation_rules": {"min": 1, "max": 365},
                "required_permission": "system:config:update"
            }
        ]
