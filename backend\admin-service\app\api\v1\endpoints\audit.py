"""
Audit log endpoints
"""

from typing import Any, List
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, HTTPException, status, Query, Response
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.user import User
from app.models.audit_log import AuditActions, AuditResourceTypes
from app.schemas.audit_log import (
    AuditLogResponse, AuditLogFilter, AuditStats, SecurityReport,
    ActivitySummary, UserActivityReport, AuditExportRequest
)
from app.services.user_service import user_service
from app.services.audit_service import audit_service

router = APIRouter()


@router.get("/", response_model=List[AuditLogResponse])
async def get_audit_logs(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    user_id: int = Query(None, description="Filter by user ID"),
    action: str = Query(None, description="Filter by action"),
    resource_type: str = Query(None, description="Filter by resource type"),
    status: str = Query(None, description="Filter by status"),
    date_from: datetime = Query(None, description="Filter from date"),
    date_to: datetime = Query(None, description="Filter to date"),
    search: str = Query(None, description="Search in description, endpoint, or user email"),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Retrieve audit logs with optional filters
    """
    # Check if user has audit read permission
    from app.services.role_service import role_service
    if not role_service.check_user_permission(db, user=current_user, permission="audit:read"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view audit logs"
        )
    
    filters = AuditLogFilter(
        user_id=user_id,
        action=action,
        resource_type=resource_type,
        status=status,
        date_from=date_from,
        date_to=date_to,
        search=search
    )
    
    logs = audit_service.get_filtered_logs(
        db, filters=filters, skip=skip, limit=limit
    )
    
    # Enhance with user information
    enhanced_logs = []
    for log in logs:
        user = db.query(User).filter(User.id == log.user_id).first() if log.user_id else None
        enhanced_log = AuditLogResponse.from_orm(log)
        if user:
            enhanced_log.user_email = user.email
            enhanced_log.user_name = user.full_name
        enhanced_logs.append(enhanced_log)
    
    return enhanced_logs


@router.get("/stats", response_model=AuditStats)
async def get_audit_statistics(
    db: Session = Depends(get_db),
    days: int = Query(30, description="Number of days to analyze"),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get audit statistics
    """
    from app.services.role_service import role_service
    if not role_service.check_user_permission(db, user=current_user, permission="audit:read"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view audit statistics"
        )
    
    # Create filter for the specified time period
    date_from = datetime.utcnow() - timedelta(days=days)
    filters = AuditLogFilter(date_from=date_from)
    
    stats = audit_service.get_audit_stats(db, filters=filters)
    return stats


@router.get("/security-report", response_model=SecurityReport)
async def get_security_report(
    db: Session = Depends(get_db),
    days: int = Query(30, description="Number of days to analyze"),
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Get security report (superuser only)
    """
    report = audit_service.get_security_report(db, days=days)
    return report


@router.get("/activity-summary", response_model=List[ActivitySummary])
async def get_activity_summary(
    db: Session = Depends(get_db),
    days: int = Query(30, description="Number of days to analyze"),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get daily activity summary
    """
    from app.services.role_service import role_service
    if not role_service.check_user_permission(db, user=current_user, permission="audit:read"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to view activity summary"
        )
    
    summary = audit_service.get_activity_summary(db, days=days)
    return summary


@router.get("/user/{user_id}/report", response_model=UserActivityReport)
async def get_user_activity_report(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get detailed activity report for a specific user
    """
    # Users can view their own reports, admins can view any user's report
    if user_id != current_user.id:
        from app.services.role_service import role_service
        if not role_service.check_user_permission(db, user=current_user, permission="audit:read"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Insufficient permissions to view user activity report"
            )
    
    try:
        report = audit_service.get_user_activity_report(db, user_id=user_id)
        return report
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )


@router.post("/export")
async def export_audit_logs(
    export_request: AuditExportRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Export audit logs in specified format
    """
    from app.services.role_service import role_service
    if not role_service.check_user_permission(db, user=current_user, permission="audit:export"):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions to export audit logs"
        )
    
    try:
        exported_data = audit_service.export_audit_logs(db, export_request=export_request)
        
        # Set appropriate content type and filename
        if export_request.format.lower() == "csv":
            media_type = "text/csv"
            filename = f"audit_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        elif export_request.format.lower() == "json":
            media_type = "application/json"
            filename = f"audit_logs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported export format"
            )
        
        # Log the export action
        from app.utils.audit import AuditLogger
        AuditLogger.log_action(
            db=db,
            user=current_user,
            action=AuditActions.EXPORT,
            resource_type=AuditResourceTypes.AUDIT,
            description=f"Exported audit logs in {export_request.format} format",
            metadata={"format": export_request.format, "filters": export_request.filters.dict()}
        )
        
        return Response(
            content=exported_data,
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/actions/list")
async def get_available_actions(
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get list of available audit actions
    """
    actions = [
        {"value": AuditActions.CREATE, "label": "Create"},
        {"value": AuditActions.READ, "label": "Read"},
        {"value": AuditActions.UPDATE, "label": "Update"},
        {"value": AuditActions.DELETE, "label": "Delete"},
        {"value": AuditActions.LOGIN, "label": "Login"},
        {"value": AuditActions.LOGOUT, "label": "Logout"},
        {"value": AuditActions.LOGIN_FAILED, "label": "Login Failed"},
        {"value": AuditActions.PASSWORD_CHANGE, "label": "Password Change"},
        {"value": AuditActions.PERMISSION_GRANT, "label": "Permission Grant"},
        {"value": AuditActions.PERMISSION_REVOKE, "label": "Permission Revoke"},
        {"value": AuditActions.EXPORT, "label": "Export"},
        {"value": AuditActions.IMPORT, "label": "Import"},
        {"value": AuditActions.PAYMENT_PROCESS, "label": "Payment Process"},
        {"value": AuditActions.PAYMENT_REFUND, "label": "Payment Refund"},
        {"value": AuditActions.CONFIG_CHANGE, "label": "Config Change"},
    ]
    return {"actions": actions}


@router.get("/resources/list")
async def get_available_resources(
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get list of available resource types
    """
    resources = [
        {"value": AuditResourceTypes.USER, "label": "User"},
        {"value": AuditResourceTypes.ROLE, "label": "Role"},
        {"value": AuditResourceTypes.PAYMENT, "label": "Payment"},
        {"value": AuditResourceTypes.FINANCIAL_RECORD, "label": "Financial Record"},
        {"value": AuditResourceTypes.SYSTEM_CONFIG, "label": "System Config"},
        {"value": AuditResourceTypes.AUTH, "label": "Authentication"},
    ]
    return {"resources": resources}
