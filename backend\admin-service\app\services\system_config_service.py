"""
System configuration service
"""

import re
from typing import List, Optional, Dict, Any
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy import func

from app.models.system_config import SystemConfig, ConfigCategory, DefaultConfigs
from app.models.user import User
from app.schemas.system_config import (
    SystemConfigCreate, SystemConfigUpdate, SystemConfigFilter,
    ConfigValidationResult, BulkConfigUpdate
)
from app.services.base_service import BaseService
from app.utils.audit import AuditLogger, AuditActions, AuditResourceTypes


class SystemConfigService(BaseService[SystemConfig, SystemConfigCreate, SystemConfigUpdate]):
    def create(self, db: Session, *, obj_in: SystemConfigCreate, created_by: User) -> SystemConfig:
        """Create a new system configuration"""
        # Check if key already exists
        existing = self.get_by_key(db, key=obj_in.key)
        if existing:
            raise ValueError(f"Configuration with key '{obj_in.key}' already exists")
        
        # Validate the configuration
        validation_result = self._validate_config_value(obj_in.value, obj_in.data_type, obj_in.validation_rules)
        if not validation_result.is_valid:
            raise ValueError(f"Invalid configuration value: {', '.join(validation_result.errors)}")
        
        db_obj = SystemConfig(
            key=obj_in.key,
            category=obj_in.category,
            name=obj_in.name,
            description=obj_in.description,
            data_type=obj_in.data_type,
            value=obj_in.value,
            default_value=obj_in.default_value,
            validation_rules=obj_in.validation_rules,
            is_active=obj_in.is_active,
            is_sensitive=obj_in.is_sensitive,
            required_permission=obj_in.required_permission,
            created_by_id=created_by.id,
            is_system=False  # User-created configs are never system configs
        )
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        # Log audit trail
        AuditLogger.log_create(
            db=db,
            user=created_by,
            resource_type=AuditResourceTypes.SYSTEM_CONFIG,
            resource_id=str(db_obj.id),
            new_values=obj_in.dict(),
            description=f"Created system configuration {obj_in.key}"
        )
        
        return db_obj

    def get_by_key(self, db: Session, *, key: str) -> Optional[SystemConfig]:
        """Get configuration by key"""
        return db.query(SystemConfig).filter(SystemConfig.key == key).first()

    def get_by_category(self, db: Session, *, category: str) -> List[SystemConfig]:
        """Get all configurations in a category"""
        return db.query(SystemConfig).filter(
            SystemConfig.category == category,
            SystemConfig.is_active == True
        ).order_by(SystemConfig.key).all()

    def get_filtered(
        self, 
        db: Session, 
        *, 
        filters: SystemConfigFilter,
        skip: int = 0,
        limit: int = 100
    ) -> List[SystemConfig]:
        """Get configurations with filters"""
        query = db.query(SystemConfig)
        
        if filters.category:
            query = query.filter(SystemConfig.category == filters.category)
        
        if filters.is_active is not None:
            query = query.filter(SystemConfig.is_active == filters.is_active)
        
        if filters.is_system is not None:
            query = query.filter(SystemConfig.is_system == filters.is_system)
        
        if filters.search:
            search_term = f"%{filters.search}%"
            query = query.filter(
                SystemConfig.key.ilike(search_term) |
                SystemConfig.name.ilike(search_term) |
                SystemConfig.description.ilike(search_term)
            )
        
        return query.order_by(SystemConfig.category, SystemConfig.key).offset(skip).limit(limit).all()

    def update_config_value(
        self, 
        db: Session, 
        *, 
        key: str, 
        value: Any, 
        updated_by: User
    ) -> SystemConfig:
        """Update configuration value"""
        config = self.get_by_key(db, key=key)
        if not config:
            raise ValueError(f"Configuration '{key}' not found")
        
        # Check permissions
        if config.required_permission:
            from app.services.role_service import role_service
            if not role_service.check_user_permission(db, user=updated_by, permission=config.required_permission):
                raise ValueError(f"Insufficient permissions to update configuration '{key}'")
        
        # Validate the new value
        validation_result = self._validate_config_value(value, config.data_type, config.validation_rules)
        if not validation_result.is_valid:
            raise ValueError(f"Invalid configuration value: {', '.join(validation_result.errors)}")
        
        old_value = config.value
        config.value = value
        config.updated_by_id = updated_by.id
        
        db.add(config)
        db.commit()
        db.refresh(config)
        
        # Log audit trail
        AuditLogger.log_action(
            db=db,
            user=updated_by,
            action=AuditActions.CONFIG_CHANGE,
            resource_type=AuditResourceTypes.SYSTEM_CONFIG,
            resource_id=str(config.id),
            old_values={"value": old_value},
            new_values={"value": value},
            description=f"Updated configuration {key}"
        )
        
        return config

    def bulk_update(
        self, 
        db: Session, 
        *, 
        updates: BulkConfigUpdate, 
        updated_by: User
    ) -> Dict[str, Any]:
        """Bulk update multiple configurations"""
        results = {"success": [], "errors": []}
        
        for key, value in updates.configs.items():
            try:
                config = self.update_config_value(db, key=key, value=value, updated_by=updated_by)
                results["success"].append({"key": key, "value": value})
            except Exception as e:
                results["errors"].append({"key": key, "error": str(e)})
        
        return results

    def get_categories(self, db: Session) -> List[Dict[str, Any]]:
        """Get all configuration categories with counts"""
        categories = db.query(
            SystemConfig.category,
            func.count(SystemConfig.id).label('count')
        ).filter(
            SystemConfig.is_active == True
        ).group_by(SystemConfig.category).all()
        
        category_info = {
            ConfigCategory.GENERAL: "General Settings",
            ConfigCategory.SECURITY: "Security Settings",
            ConfigCategory.EMAIL: "Email Configuration",
            ConfigCategory.PAYMENT: "Payment Settings",
            ConfigCategory.NOTIFICATION: "Notification Settings",
            ConfigCategory.APPEARANCE: "Appearance Settings",
            ConfigCategory.INTEGRATION: "Integration Settings",
            ConfigCategory.BACKUP: "Backup Settings",
            ConfigCategory.LOGGING: "Logging Settings",
        }
        
        return [
            {
                "category": cat.category,
                "name": category_info.get(cat.category, cat.category.title()),
                "count": cat.count
            }
            for cat in categories
        ]

    def initialize_default_configs(self, db: Session, created_by: User) -> int:
        """Initialize default system configurations"""
        default_configs = DefaultConfigs.get_default_configs()
        created_count = 0
        
        for config_data in default_configs:
            existing = self.get_by_key(db, key=config_data["key"])
            if not existing:
                db_obj = SystemConfig(
                    **config_data,
                    value=config_data["default_value"],
                    created_by_id=created_by.id
                )
                db.add(db_obj)
                created_count += 1
        
        if created_count > 0:
            db.commit()
            
            # Log audit trail
            AuditLogger.log_action(
                db=db,
                user=created_by,
                action=AuditActions.CREATE,
                resource_type=AuditResourceTypes.SYSTEM_CONFIG,
                resource_id="bulk",
                new_values={"count": created_count},
                description=f"Initialized {created_count} default system configurations"
            )
        
        return created_count

    def _validate_config_value(
        self, 
        value: Any, 
        data_type: str, 
        validation_rules: Optional[Dict[str, Any]] = None
    ) -> ConfigValidationResult:
        """Validate configuration value against data type and rules"""
        errors = []
        warnings = []
        
        # Type validation
        if data_type == "string":
            if value is not None and not isinstance(value, str):
                try:
                    value = str(value)
                except:
                    errors.append("Value must be a string")
        elif data_type == "number":
            if value is not None and not isinstance(value, (int, float)):
                try:
                    value = float(value)
                except:
                    errors.append("Value must be a number")
        elif data_type == "boolean":
            if value is not None and not isinstance(value, bool):
                if isinstance(value, str):
                    value = value.lower() in ['true', '1', 'yes', 'on']
                else:
                    errors.append("Value must be a boolean")
        
        # Validation rules
        if validation_rules and value is not None:
            # Required validation
            if validation_rules.get("required") and (value is None or value == ""):
                errors.append("Value is required")
            
            # Min/Max validation for numbers
            if data_type == "number" and isinstance(value, (int, float)):
                if "min" in validation_rules and value < validation_rules["min"]:
                    errors.append(f"Value must be at least {validation_rules['min']}")
                if "max" in validation_rules and value > validation_rules["max"]:
                    errors.append(f"Value must be at most {validation_rules['max']}")
            
            # Pattern validation for strings
            if data_type == "string" and isinstance(value, str) and "pattern" in validation_rules:
                pattern = validation_rules["pattern"]
                if not re.match(pattern, value):
                    errors.append(f"Value does not match required pattern")
        
        return ConfigValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings
        )

    def get_config_value(self, db: Session, *, key: str, default: Any = None) -> Any:
        """Get configuration value with fallback to default"""
        config = self.get_by_key(db, key=key)
        if not config or not config.is_active:
            return default
        
        return config.get_typed_value()

    def reset_to_default(self, db: Session, *, key: str, updated_by: User) -> SystemConfig:
        """Reset configuration to default value"""
        config = self.get_by_key(db, key=key)
        if not config:
            raise ValueError(f"Configuration '{key}' not found")
        
        old_value = config.value
        config.value = config.default_value
        config.updated_by_id = updated_by.id
        
        db.add(config)
        db.commit()
        db.refresh(config)
        
        # Log audit trail
        AuditLogger.log_action(
            db=db,
            user=updated_by,
            action=AuditActions.CONFIG_CHANGE,
            resource_type=AuditResourceTypes.SYSTEM_CONFIG,
            resource_id=str(config.id),
            old_values={"value": old_value},
            new_values={"value": config.value},
            description=f"Reset configuration {key} to default value"
        )
        
        return config


# Create system config service instance
system_config_service = SystemConfigService(SystemConfig)
