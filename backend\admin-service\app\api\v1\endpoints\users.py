"""
User management endpoints
"""

from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.user import User
from app.schemas.user import UserCreate, UserResponse, UserUpdate
from app.schemas.role import RoleAssignment
from app.services.user_service import user_service
from app.services.role_service import role_service

router = APIRouter()


@router.get("/me", response_model=UserResponse)
async def read_user_me(
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get current user
    """
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_user_me(
    *,
    db: Session = Depends(get_db),
    user_in: UserUpdate,
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Update current user
    """
    user = user_service.update(db, db_obj=current_user, obj_in=user_in)
    return user


@router.get("/", response_model=List[UserResponse])
async def read_users(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Retrieve users (admin only)
    """
    users = user_service.get_multi(db, skip=skip, limit=limit)
    return users


@router.post("/", response_model=UserResponse)
async def create_user(
    *,
    db: Session = Depends(get_db),
    user_in: UserCreate,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Create new user (admin only)
    """
    user = user_service.get_by_email(db, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The user with this email already exists in the system."
        )
    user = user_service.create(db, obj_in=user_in)
    return user


@router.get("/{user_id}", response_model=UserResponse)
async def read_user_by_id(
    user_id: int,
    current_user: User = Depends(user_service.get_current_active_user),
    db: Session = Depends(get_db)
) -> Any:
    """
    Get a specific user by id
    """
    user = user_service.get(db, id=user_id)
    if user == current_user:
        return user
    if not user_service.is_superuser(current_user):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="The user doesn't have enough privileges"
        )
    return user


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    *,
    db: Session = Depends(get_db),
    user_id: int,
    user_in: UserUpdate,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Update a user (admin only)
    """
    user = user_service.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="The user with this id does not exist in the system"
        )
    user = user_service.update(db, db_obj=user, obj_in=user_in)
    return user


@router.delete("/{user_id}")
async def delete_user(
    *,
    db: Session = Depends(get_db),
    user_id: int,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Delete a user (admin only)
    """
    user = user_service.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="The user with this id does not exist in the system"
        )
    if user == current_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Users cannot delete themselves"
        )
    user_service.remove(db, id=user_id)
    return {"message": "User deleted successfully"}


@router.post("/{user_id}/assign-role", response_model=UserResponse)
async def assign_role_to_user(
    *,
    db: Session = Depends(get_db),
    user_id: int,
    role_assignment: RoleAssignment,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Assign role to user (admin only)
    """
    user = user_service.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    role = role_service.get(db, id=role_assignment.role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )

    user = role_service.assign_role_to_user(
        db, user=user, role=role, assigned_by=current_user
    )
    return user


@router.delete("/{user_id}/remove-role", response_model=UserResponse)
async def remove_role_from_user(
    *,
    db: Session = Depends(get_db),
    user_id: int,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Remove role from user (admin only)
    """
    user = user_service.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    user = role_service.remove_role_from_user(
        db, user=user, removed_by=current_user
    )
    return user


@router.get("/{user_id}/permissions")
async def get_user_permissions(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Get user permissions (admin only)
    """
    user = user_service.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    permissions = role_service.get_user_permissions(db, user=user)
    return {"user_id": user_id, "permissions": permissions}
