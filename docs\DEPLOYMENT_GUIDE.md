# 🚀 Innovative Centre Platform - Deployment Guide

## 📋 Prerequisites

### System Requirements

**Minimum Requirements:**
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+

**Recommended Requirements:**
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **OS**: Ubuntu 22.04 LTS

### Software Dependencies

**Required Software:**
- **Docker**: 24.0+
- **Docker Compose**: 2.20+
- **Git**: 2.30+
- **Node.js**: 20.0+ (for frontend builds)
- **Python**: 3.13+ (for backend)

## 🐳 Docker Deployment (Recommended)

### 1. Clone Repository

```bash
git clone <repository-url>
cd innovative-platform
```

### 2. Environment Configuration

```bash
# Copy environment templates
cp backend/admin-service/.env.example backend/admin-service/.env
cp frontend/admin-portal/.env.example frontend/admin-portal/.env

# Edit environment files
nano backend/admin-service/.env
nano frontend/admin-portal/.env
```

### 3. Environment Variables

**Backend (.env):**
```env
# Database
DATABASE_URL=**************************************/innovative_centre
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=innovative_centre

# Redis
REDIS_URL=redis://redis:6379/0

# Security
SECRET_KEY=your_very_secure_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3004", "https://yourdomain.com"]

# Email (Optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password

# Environment
ENVIRONMENT=production
DEBUG=false
```

**Frontend (.env):**
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=Innovative Centre Platform
NEXT_PUBLIC_ENVIRONMENT=production
```

### 4. Build and Deploy

```bash
# Build and start all services
docker-compose up -d --build

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

### 5. Database Setup

```bash
# Run database migrations
docker-compose exec admin-service python -m alembic upgrade head

# Create initial admin user (optional)
docker-compose exec admin-service python create_admin.py
```

## 🖥️ Manual Deployment

### Backend Deployment

```bash
# Navigate to backend
cd backend/admin-service

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Set environment variables
export DATABASE_URL="postgresql://user:pass@localhost/db"
export SECRET_KEY="your-secret-key"
export REDIS_URL="redis://localhost:6379/0"

# Run migrations
python -m alembic upgrade head

# Start application
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

### Frontend Deployment

```bash
# Navigate to frontend
cd frontend/admin-portal

# Install dependencies
npm install

# Build for production
npm run build

# Start production server
npm start

# Or use PM2 for process management
npm install -g pm2
pm2 start npm --name "admin-portal" -- start
```

## 🌐 Production Deployment

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Nginx
sudo apt install nginx -y
```

### 2. SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. Nginx Configuration

```nginx
# /etc/nginx/sites-available/innovative-centre
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # Frontend
    location / {
        proxy_pass http://localhost:3004;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

### 4. Enable Nginx Configuration

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/innovative-centre /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

### 5. Production Docker Compose

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    restart: unless-stopped
    networks:
      - app-network
    volumes:
      - redis_data:/data

  admin-service:
    build:
      context: ./backend/admin-service
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - DEBUG=false
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - app-network
    ports:
      - "8000:8000"

  admin-portal:
    build:
      context: ./frontend/admin-portal
      dockerfile: Dockerfile
    environment:
      - NEXT_PUBLIC_API_URL=https://yourdomain.com/api
      - NEXT_PUBLIC_ENVIRONMENT=production
    restart: unless-stopped
    networks:
      - app-network
    ports:
      - "3004:3000"

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

## 🔧 Configuration Management

### Environment-Specific Configs

**Development:**
```bash
docker-compose -f docker-compose.yml up -d
```

**Staging:**
```bash
docker-compose -f docker-compose.staging.yml up -d
```

**Production:**
```bash
docker-compose -f docker-compose.prod.yml up -d
```

### Secrets Management

```bash
# Use Docker secrets for sensitive data
echo "your_secret_key" | docker secret create secret_key -
echo "your_db_password" | docker secret create db_password -
```

## 📊 Monitoring & Logging

### Application Monitoring

```bash
# Install monitoring tools
docker run -d \
  --name prometheus \
  -p 9090:9090 \
  prom/prometheus

docker run -d \
  --name grafana \
  -p 3000:3000 \
  grafana/grafana
```

### Log Management

```bash
# Configure log rotation
sudo nano /etc/logrotate.d/innovative-centre

# Content:
/var/log/innovative-centre/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### Health Checks

```bash
# Backend health check
curl http://localhost:8000/health

# Frontend health check
curl http://localhost:3004/api/health

# Database health check
docker-compose exec db pg_isready -U postgres
```

## 🔄 Backup & Recovery

### Database Backup

```bash
# Create backup script
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec -T db pg_dump -U postgres innovative_centre > backup_$DATE.sql
gzip backup_$DATE.sql

# Schedule with cron
0 2 * * * /path/to/backup.sh
```

### Application Backup

```bash
# Backup application data
tar -czf app_backup_$(date +%Y%m%d).tar.gz \
  backend/admin-service/uploads \
  frontend/admin-portal/.next \
  docker-compose.yml \
  .env
```

### Recovery Process

```bash
# Restore database
gunzip backup_20240101_020000.sql.gz
docker-compose exec -T db psql -U postgres innovative_centre < backup_20240101_020000.sql

# Restore application
tar -xzf app_backup_20240101.tar.gz
docker-compose up -d --build
```

## 🚨 Troubleshooting

### Common Issues

**Database Connection:**
```bash
# Check database logs
docker-compose logs db

# Test connection
docker-compose exec admin-service python -c "from app.db.session import engine; print(engine.execute('SELECT 1').scalar())"
```

**Memory Issues:**
```bash
# Check memory usage
docker stats

# Increase memory limits in docker-compose.yml
services:
  admin-service:
    deploy:
      resources:
        limits:
          memory: 1G
```

**SSL Certificate Issues:**
```bash
# Renew certificate
sudo certbot renew

# Check certificate status
sudo certbot certificates
```

### Performance Optimization

```bash
# Enable gzip compression in Nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# Optimize Docker images
docker system prune -a
docker volume prune
```

## 📞 Support

### Deployment Support

- **Documentation**: Check this deployment guide
- **Logs**: Always check application and system logs
- **Community**: Join our community forum
- **Professional Support**: Contact for enterprise support

### Emergency Procedures

1. **Service Down**: Check Docker containers and restart if needed
2. **Database Issues**: Check database logs and connection
3. **SSL Expiry**: Renew certificates immediately
4. **Security Incident**: Follow incident response procedures

---

**Note**: Always test deployments in a staging environment before production deployment.
