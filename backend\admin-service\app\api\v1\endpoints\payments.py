"""
Payment management endpoints
"""

from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.user import User
from app.models.payment import PaymentStatus, PaymentMethod
from app.schemas.payment import (
    PaymentCreate, PaymentResponse, PaymentUpdate, PaymentFilter,
    PaymentProcessRequest, PaymentRefundRequest, PaymentStats
)
from app.services.user_service import user_service
from app.services.payment_service import payment_service

router = APIRouter()


@router.get("/", response_model=List[PaymentResponse])
async def get_payments(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    status: str = Query(None, description="Filter by payment status"),
    payment_method: str = Query(None, description="Filter by payment method"),
    user_id: int = Query(None, description="Filter by user ID"),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Retrieve payments with optional filters
    """
    filters = PaymentFilter(
        status=status,
        payment_method=payment_method,
        user_id=user_id
    )
    
    payments = payment_service.get_filtered(
        db, filters=filters, skip=skip, limit=limit
    )
    return payments


@router.post("/", response_model=PaymentResponse)
async def create_payment(
    *,
    db: Session = Depends(get_db),
    payment_in: PaymentCreate,
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Create new payment record
    """
    payment = payment_service.create(db, obj_in=payment_in, created_by=current_user)
    return payment


@router.get("/{payment_id}", response_model=PaymentResponse)
async def get_payment(
    payment_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get payment by payment ID
    """
    payment = payment_service.get_by_payment_id(db, payment_id=payment_id)
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    return payment


@router.put("/{payment_id}", response_model=PaymentResponse)
async def update_payment(
    *,
    db: Session = Depends(get_db),
    payment_id: str,
    payment_in: PaymentUpdate,
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Update payment
    """
    payment = payment_service.get_by_payment_id(db, payment_id=payment_id)
    if not payment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Payment not found"
        )
    
    payment = payment_service.update(db, db_obj=payment, obj_in=payment_in)
    return payment


@router.post("/{payment_id}/process", response_model=PaymentResponse)
async def process_payment(
    *,
    db: Session = Depends(get_db),
    payment_id: str,
    process_request: PaymentProcessRequest,
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Process a payment (mark as completed)
    """
    try:
        payment = payment_service.process_payment(
            db,
            payment_id=payment_id,
            transaction_id=process_request.transaction_id,
            gateway_response=process_request.gateway_response,
            processed_by=current_user
        )
        return payment
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{payment_id}/refund", response_model=PaymentResponse)
async def refund_payment(
    *,
    db: Session = Depends(get_db),
    payment_id: str,
    refund_request: PaymentRefundRequest,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Refund a payment (requires superuser privileges)
    """
    try:
        payment = payment_service.refund_payment(
            db,
            payment_id=payment_id,
            refund_amount=refund_request.refund_amount,
            refund_reason=refund_request.refund_reason,
            processed_by=current_user
        )
        return payment
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/stats/summary", response_model=PaymentStats)
async def get_payment_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get payment statistics summary
    """
    stats = payment_service.get_stats(db)
    return stats


@router.get("/methods/list")
async def get_payment_methods(
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get available payment methods
    """
    return {
        "payment_methods": [
            {"value": PaymentMethod.CARD, "label": "Credit/Debit Card"},
            {"value": PaymentMethod.BANK_TRANSFER, "label": "Bank Transfer"},
            {"value": PaymentMethod.CASH, "label": "Cash"},
            {"value": PaymentMethod.DIGITAL_WALLET, "label": "Digital Wallet"},
            {"value": PaymentMethod.CHECK, "label": "Check"},
        ]
    }


@router.get("/statuses/list")
async def get_payment_statuses(
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get available payment statuses
    """
    return {
        "payment_statuses": [
            {"value": PaymentStatus.PENDING, "label": "Pending"},
            {"value": PaymentStatus.PROCESSING, "label": "Processing"},
            {"value": PaymentStatus.COMPLETED, "label": "Completed"},
            {"value": PaymentStatus.FAILED, "label": "Failed"},
            {"value": PaymentStatus.CANCELLED, "label": "Cancelled"},
            {"value": PaymentStatus.REFUNDED, "label": "Refunded"},
            {"value": PaymentStatus.PARTIALLY_REFUNDED, "label": "Partially Refunded"},
        ]
    }
