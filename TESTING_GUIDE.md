# 🧪 **Innovative Centre Platform - Testing Guide**

## 🚀 **Quick Start Testing**

### **Prerequisites Installation**

1. **Install Python 3.11+**
   ```bash
   # Download from https://www.python.org/downloads/
   # Ensure "Add Python to PATH" is checked during installation
   ```

2. **Install Node.js 20+**
   ```bash
   # Download from https://nodejs.org/
   # This includes npm package manager
   ```

3. **Install Docker Desktop** (Optional but recommended)
   ```bash
   # Download from https://www.docker.com/products/docker-desktop/
   ```

### **Method 1: Docker Testing (Easiest)**

```bash
# 1. Navigate to project directory
cd "C:\Users\<USER>\Documents\augment-projects\Innovative Platform"

# 2. Start with Docker Compose
docker-compose up -d

# 3. Wait for services to start (2-3 minutes)
# 4. Access the application:
#    - Frontend: http://localhost:3004
#    - API Docs: http://localhost:8000/docs
#    - Health Check: http://localhost:8000/health
```

### **Method 2: Manual Testing**

#### **Backend Setup**
```bash
# 1. Navigate to backend
cd backend/admin-service

# 2. Create virtual environment
python -m venv venv

# 3. Activate virtual environment
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 4. Install dependencies
pip install -r requirements.txt

# 5. Set environment variables
# Copy .env.example to .env and configure

# 6. Start the backend
python main.py
```

#### **Frontend Setup**
```bash
# 1. Navigate to frontend (new terminal)
cd frontend/admin-portal

# 2. Install dependencies
npm install

# 3. Start development server
npm run dev
```

## 🔍 **Testing Scenarios**

### **1. Authentication Testing**

**Default Login Credentials:**
- **Email**: `<EMAIL>`
- **Password**: `changeme`

**Test Cases:**
- ✅ Valid login
- ✅ Invalid credentials
- ✅ Password reset flow
- ✅ Session timeout
- ✅ Logout functionality

### **2. User Management Testing**

**Test Cases:**
- ✅ Create new user
- ✅ Assign roles (Admin, Manager, Cashier, etc.)
- ✅ Update user information
- ✅ Deactivate/activate users
- ✅ Role-based access control

**Sample Test Data:**
```json
{
  "email": "<EMAIL>",
  "username": "testuser",
  "full_name": "Test User",
  "password": "TestPassword123"
}
```

### **3. Payment Management Testing**

**Test Cases:**
- ✅ Record new payment
- ✅ Process payment
- ✅ Refund payment
- ✅ Payment search and filtering
- ✅ Payment statistics

**Sample Payment Data:**
```json
{
  "student_name": "John Doe",
  "student_email": "<EMAIL>",
  "amount": 150.00,
  "payment_method": "card",
  "description": "Course enrollment fee",
  "course_name": "Python Programming"
}
```

### **4. Financial Management Testing**

**Test Cases:**
- ✅ Record income
- ✅ Record expenses
- ✅ Approve financial records
- ✅ Financial dashboard
- ✅ Monthly reports

**Sample Financial Data:**
```json
{
  "type": "income",
  "category": "course_fees",
  "amount": 500.00,
  "description": "Course enrollment fees",
  "reference_number": "REF001"
}
```

### **5. System Configuration Testing**

**Test Cases:**
- ✅ Update system settings
- ✅ Email configuration
- ✅ Security settings
- ✅ Bulk configuration updates

### **6. Audit Logging Testing**

**Test Cases:**
- ✅ View audit logs
- ✅ Filter by user/action/date
- ✅ Export audit logs
- ✅ Security reports

## 🔧 **API Testing**

### **Using curl (Command Line)**

```bash
# 1. Health Check
curl http://localhost:8000/health

# 2. Login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=changeme"

# 3. Get Users (with token)
curl -X GET "http://localhost:8000/api/v1/users/" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### **Using Swagger UI**

1. Open: `http://localhost:8000/docs`
2. Click "Authorize" button
3. Login to get token
4. Test all endpoints interactively

## 🧪 **Automated Testing**

### **Backend Tests**

```bash
# Navigate to backend
cd backend/admin-service

# Run all tests
python run_tests.py

# Run specific test files
python -m pytest tests/test_auth.py -v
python -m pytest tests/test_users.py -v
python -m pytest tests/test_payments.py -v
```

### **Frontend Tests**

```bash
# Navigate to frontend
cd frontend/admin-portal

# Run tests (when implemented)
npm test

# Type checking
npm run type-check

# Linting
npm run lint
```

## 📊 **Performance Testing**

### **Load Testing**

```bash
# Install Apache Bench
# Windows: Download from Apache website
# Linux: sudo apt-get install apache2-utils

# Test API endpoints
ab -n 100 -c 10 http://localhost:8000/health
ab -n 50 -c 5 http://localhost:8000/api/v1/users/
```

### **Database Performance**

```bash
# Check database queries
# Monitor logs for slow queries
# Use database profiling tools
```

## 🔒 **Security Testing**

### **Authentication Security**

- ✅ Test rate limiting on login
- ✅ Test password complexity
- ✅ Test session management
- ✅ Test CORS policies

### **Input Validation**

- ✅ Test SQL injection protection
- ✅ Test XSS protection
- ✅ Test file upload security
- ✅ Test API input validation

## 🐛 **Common Issues & Solutions**

### **Backend Issues**

**Issue**: Database connection error
**Solution**: Check PostgreSQL is running and credentials are correct

**Issue**: Import errors
**Solution**: Ensure virtual environment is activated and dependencies installed

**Issue**: Port already in use
**Solution**: Change port in main.py or kill existing process

### **Frontend Issues**

**Issue**: Module not found
**Solution**: Run `npm install` to install dependencies

**Issue**: API connection error
**Solution**: Ensure backend is running on correct port

**Issue**: Build errors
**Solution**: Check TypeScript errors with `npm run type-check`

## 📝 **Test Checklist**

### **Basic Functionality**
- [ ] Application starts without errors
- [ ] Login page loads
- [ ] Authentication works
- [ ] Dashboard displays
- [ ] Navigation works
- [ ] All main features accessible

### **User Management**
- [ ] Create user
- [ ] Edit user
- [ ] Assign roles
- [ ] Delete user
- [ ] User search/filter

### **Payment Management**
- [ ] Record payment
- [ ] Process payment
- [ ] Refund payment
- [ ] Payment reports
- [ ] Payment search

### **Financial Management**
- [ ] Add income record
- [ ] Add expense record
- [ ] Approve records
- [ ] Financial dashboard
- [ ] Export reports

### **System Features**
- [ ] System configuration
- [ ] Audit logs
- [ ] User activity tracking
- [ ] Security features
- [ ] Performance monitoring

## 🎯 **Success Criteria**

✅ **All core features working**
✅ **No critical errors in logs**
✅ **Responsive design on different screen sizes**
✅ **API endpoints responding correctly**
✅ **Database operations successful**
✅ **Security measures functioning**
✅ **Performance within acceptable limits**

---

**Need Help?** Check the logs in:
- Backend: `backend/admin-service/logs/`
- Frontend: Browser developer console
- Docker: `docker-compose logs`
