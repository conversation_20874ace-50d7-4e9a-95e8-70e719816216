# Production Environment Configuration
# Copy this file to .env.prod and update with your production values

# ================================
# DATABASE CONFIGURATION
# ================================
POSTGRES_DB=innovative_centre
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_very_secure_database_password_here
DATABASE_HOST=db
DATABASE_PORT=5432

# ================================
# REDIS CONFIGURATION
# ================================
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_HOST=redis
REDIS_PORT=6379

# ================================
# APPLICATION SECURITY
# ================================
SECRET_KEY=your_very_long_and_secure_secret_key_at_least_32_characters_long
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# ================================
# CORS AND ALLOWED HOSTS
# ================================
BACKEND_CORS_ORIGINS=["https://yourdomain.com", "https://www.yourdomain.com"]
ALLOWED_HOSTS=["yourdomain.com", "www.yourdomain.com", "localhost"]

# ================================
# FRONTEND CONFIGURATION
# ================================
NEXT_PUBLIC_API_URL=https://yourdomain.com/api
NEXT_PUBLIC_APP_NAME=Innovative Centre Platform
NEXT_PUBLIC_ENVIRONMENT=production

# ================================
# EMAIL CONFIGURATION
# ================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_specific_password
EMAIL_USE_TLS=true
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Innovative Centre Platform

# ================================
# SSL/TLS CONFIGURATION
# ================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# ================================
# MONITORING AND LOGGING
# ================================
LOG_LEVEL=INFO
SENTRY_DSN=https://<EMAIL>/project_id

# Grafana Configuration
GRAFANA_PASSWORD=your_secure_grafana_password

# ================================
# BACKUP CONFIGURATION
# ================================
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1

# ================================
# PERFORMANCE SETTINGS
# ================================
WORKERS=4
MAX_CONNECTIONS=100
POOL_SIZE=20
POOL_OVERFLOW=30

# ================================
# SECURITY SETTINGS
# ================================
RATE_LIMIT_PER_MINUTE=60
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15
SESSION_TIMEOUT_MINUTES=120

# ================================
# FEATURE FLAGS
# ================================
ENABLE_REGISTRATION=false
ENABLE_PASSWORD_RESET=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_TWO_FACTOR_AUTH=false
ENABLE_AUDIT_LOGGING=true

# ================================
# PAYMENT CONFIGURATION
# ================================
PAYMENT_CURRENCY=USD
TAX_RATE=0.08
PAYMENT_METHODS=["card", "cash", "bank_transfer", "check"]

# ================================
# FILE UPLOAD SETTINGS
# ================================
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_TYPES=["pdf", "doc", "docx", "xls", "xlsx", "jpg", "jpeg", "png"]
UPLOAD_PATH=/app/uploads

# ================================
# CACHE SETTINGS
# ================================
CACHE_TTL_SECONDS=300
CACHE_MAX_ENTRIES=10000

# ================================
# DOMAIN AND URL CONFIGURATION
# ================================
DOMAIN=yourdomain.com
PROTOCOL=https
BASE_URL=https://yourdomain.com

# ================================
# DOCKER CONFIGURATION
# ================================
COMPOSE_PROJECT_NAME=innovative_centre
DOCKER_REGISTRY=your-registry.com
IMAGE_TAG=latest

# ================================
# HEALTH CHECK CONFIGURATION
# ================================
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3

# ================================
# TIMEZONE CONFIGURATION
# ================================
TZ=UTC
DEFAULT_TIMEZONE=America/New_York

# ================================
# MAINTENANCE MODE
# ================================
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="System is under maintenance. Please try again later."

# ================================
# API RATE LIMITING
# ================================
API_RATE_LIMIT_PER_HOUR=1000
API_BURST_LIMIT=50

# ================================
# SESSION CONFIGURATION
# ================================
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=Strict

# ================================
# DEVELOPMENT FLAGS (SET TO FALSE IN PRODUCTION)
# ================================
DEBUG=false
TESTING=false
DEVELOPMENT=false

# ================================
# EXTERNAL INTEGRATIONS
# ================================
# Add your external service configurations here
# STRIPE_SECRET_KEY=sk_live_...
# TWILIO_ACCOUNT_SID=AC...
# SENDGRID_API_KEY=SG...

# ================================
# CUSTOM APPLICATION SETTINGS
# ================================
ORGANIZATION_NAME=Innovative Centre
ORGANIZATION_EMAIL=<EMAIL>
ORGANIZATION_PHONE=******-123-4567
ORGANIZATION_ADDRESS=123 Education St, Learning City, LC 12345

# Default admin user (only used for initial setup)
INITIAL_ADMIN_EMAIL=<EMAIL>
INITIAL_ADMIN_PASSWORD=change_me_immediately
CREATE_INITIAL_DATA=true
