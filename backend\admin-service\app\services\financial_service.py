"""
Financial service for financial record management
"""

import uuid
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from decimal import Decimal

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, extract

from app.models.financial_record import (
    FinancialRecord, FinancialRecordType, FinancialRecordStatus,
    IncomeCategory, ExpenseCategory
)
from app.models.user import User
from app.schemas.financial_record import (
    FinancialRecordCreate, FinancialRecordUpdate, FinancialRecordFilter,
    FinancialStats, FinancialDashboard
)
from app.services.base_service import BaseService
from app.utils.audit import AuditLogger, AuditActions, AuditResourceTypes


class FinancialService(BaseService[FinancialRecord, FinancialRecordCreate, FinancialRecordUpdate]):
    def create(self, db: Session, *, obj_in: FinancialRecordCreate, created_by: User) -> FinancialRecord:
        """Create a new financial record"""
        # Generate record number if not provided
        record_number = obj_in.record_number or self._generate_record_number(obj_in.record_type)
        
        # Create financial record object
        obj_in_data = obj_in.dict(exclude={"record_number"})
        db_obj = FinancialRecord(
            record_number=record_number,
            created_by_id=created_by.id,
            **obj_in_data
        )
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        # Log audit trail
        AuditLogger.log_create(
            db=db,
            user=created_by,
            resource_type=AuditResourceTypes.FINANCIAL_RECORD,
            resource_id=str(db_obj.id),
            new_values=obj_in.dict(),
            description=f"Created financial record {record_number}"
        )
        
        return db_obj

    def _generate_record_number(self, record_type: str) -> str:
        """Generate a unique record number"""
        prefix = "INC" if record_type == FinancialRecordType.INCOME else "EXP"
        timestamp = datetime.now().strftime("%Y%m%d")
        unique_id = uuid.uuid4().hex[:6].upper()
        return f"{prefix}-{timestamp}-{unique_id}"

    def approve_record(self, db: Session, *, record_id: int, approved_by: User) -> FinancialRecord:
        """Approve a financial record"""
        record = self.get(db, id=record_id)
        if not record:
            raise ValueError(f"Financial record {record_id} not found")
        
        if record.status != FinancialRecordStatus.PENDING:
            raise ValueError(f"Record {record.record_number} is not in pending status")
        
        old_values = {
            "status": record.status,
            "approved_at": record.approved_at
        }
        
        # Update record
        record.status = FinancialRecordStatus.APPROVED
        record.approved_at = datetime.utcnow()
        
        db.add(record)
        db.commit()
        db.refresh(record)
        
        # Log audit trail
        AuditLogger.log_action(
            db=db,
            user=approved_by,
            action=AuditActions.UPDATE,
            resource_type=AuditResourceTypes.FINANCIAL_RECORD,
            resource_id=str(record.id),
            old_values=old_values,
            new_values={
                "status": record.status,
                "approved_at": record.approved_at.isoformat() if record.approved_at else None
            },
            description=f"Approved financial record {record.record_number}"
        )
        
        return record

    def get_by_record_number(self, db: Session, *, record_number: str) -> Optional[FinancialRecord]:
        """Get financial record by record number"""
        return db.query(FinancialRecord).filter(FinancialRecord.record_number == record_number).first()

    def get_filtered(
        self, 
        db: Session, 
        *, 
        filters: FinancialRecordFilter,
        skip: int = 0,
        limit: int = 100
    ) -> List[FinancialRecord]:
        """Get financial records with filters"""
        query = db.query(FinancialRecord)
        
        if filters.record_type:
            query = query.filter(FinancialRecord.record_type == filters.record_type)
        
        if filters.category:
            query = query.filter(FinancialRecord.category == filters.category)
        
        if filters.status:
            query = query.filter(FinancialRecord.status == filters.status)
        
        if filters.user_id:
            query = query.filter(FinancialRecord.user_id == filters.user_id)
        
        if filters.date_from:
            query = query.filter(FinancialRecord.transaction_date >= filters.date_from)
        
        if filters.date_to:
            query = query.filter(FinancialRecord.transaction_date <= filters.date_to)
        
        if filters.amount_min:
            query = query.filter(FinancialRecord.amount >= filters.amount_min)
        
        if filters.amount_max:
            query = query.filter(FinancialRecord.amount <= filters.amount_max)
        
        if filters.is_recurring is not None:
            query = query.filter(FinancialRecord.is_recurring == filters.is_recurring)
        
        return query.order_by(FinancialRecord.transaction_date.desc()).offset(skip).limit(limit).all()

    def get_stats(self, db: Session, *, filters: Optional[FinancialRecordFilter] = None) -> FinancialStats:
        """Get financial statistics"""
        query = db.query(FinancialRecord)
        
        # Apply filters if provided
        if filters:
            if filters.date_from:
                query = query.filter(FinancialRecord.transaction_date >= filters.date_from)
            if filters.date_to:
                query = query.filter(FinancialRecord.transaction_date <= filters.date_to)
        
        # Get all records for stats
        records = query.all()
        
        # Calculate totals
        income_records = [r for r in records if r.record_type == FinancialRecordType.INCOME]
        expense_records = [r for r in records if r.record_type == FinancialRecordType.EXPENSE]
        
        total_income = sum(r.amount for r in income_records)
        total_expenses = sum(r.amount for r in expense_records)
        net_profit = total_income - total_expenses
        
        # Pending amounts
        pending_income = sum(r.amount for r in income_records if r.status == FinancialRecordStatus.PENDING)
        pending_expenses = sum(r.amount for r in expense_records if r.status == FinancialRecordStatus.PENDING)
        
        # Approved amounts
        approved_income = sum(r.amount for r in income_records if r.status == FinancialRecordStatus.APPROVED)
        approved_expenses = sum(r.amount for r in expense_records if r.status == FinancialRecordStatus.APPROVED)
        
        # Tax amount
        tax_amount = sum(r.tax_amount or Decimal('0') for r in records)
        
        return FinancialStats(
            total_income=total_income,
            total_expenses=total_expenses,
            net_profit=net_profit,
            pending_income=pending_income,
            pending_expenses=pending_expenses,
            approved_income=approved_income,
            approved_expenses=approved_expenses,
            tax_amount=tax_amount
        )

    def get_dashboard_data(self, db: Session) -> FinancialDashboard:
        """Get financial dashboard data"""
        # Get current month stats
        current_month = datetime.now().replace(day=1)
        filters = FinancialRecordFilter(date_from=current_month)
        stats = self.get_stats(db, filters=filters)
        
        # Get recent transactions (last 10)
        recent_transactions = self.get_filtered(db, filters=FinancialRecordFilter(), limit=10)
        
        # Get income by category
        income_by_category = self._get_category_breakdown(db, FinancialRecordType.INCOME)
        
        # Get expenses by category
        expenses_by_category = self._get_category_breakdown(db, FinancialRecordType.EXPENSE)
        
        # Get monthly trend (last 12 months)
        monthly_trend = self._get_monthly_trend(db)
        
        return FinancialDashboard(
            stats=stats,
            recent_transactions=recent_transactions,
            income_by_category=income_by_category,
            expenses_by_category=expenses_by_category,
            monthly_trend=monthly_trend
        )

    def _get_category_breakdown(self, db: Session, record_type: str) -> List[Dict[str, Any]]:
        """Get breakdown by category"""
        query = db.query(
            FinancialRecord.category,
            func.sum(FinancialRecord.amount).label('total')
        ).filter(
            FinancialRecord.record_type == record_type,
            FinancialRecord.status == FinancialRecordStatus.APPROVED
        ).group_by(FinancialRecord.category)
        
        results = query.all()
        return [{"category": r.category, "amount": float(r.total)} for r in results]

    def _get_monthly_trend(self, db: Session) -> List[Dict[str, Any]]:
        """Get monthly trend for the last 12 months"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365)
        
        query = db.query(
            extract('year', FinancialRecord.transaction_date).label('year'),
            extract('month', FinancialRecord.transaction_date).label('month'),
            FinancialRecord.record_type,
            func.sum(FinancialRecord.amount).label('total')
        ).filter(
            FinancialRecord.transaction_date >= start_date,
            FinancialRecord.status == FinancialRecordStatus.APPROVED
        ).group_by(
            extract('year', FinancialRecord.transaction_date),
            extract('month', FinancialRecord.transaction_date),
            FinancialRecord.record_type
        ).order_by('year', 'month')
        
        results = query.all()
        
        # Process results into monthly trend
        monthly_data = {}
        for r in results:
            month_key = f"{int(r.year)}-{int(r.month):02d}"
            if month_key not in monthly_data:
                monthly_data[month_key] = {"month": month_key, "income": 0, "expenses": 0}
            
            if r.record_type == FinancialRecordType.INCOME:
                monthly_data[month_key]["income"] = float(r.total)
            else:
                monthly_data[month_key]["expenses"] = float(r.total)
        
        return list(monthly_data.values())


# Create financial service instance
financial_service = FinancialService(FinancialRecord)
