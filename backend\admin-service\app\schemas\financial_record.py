"""
Financial record schemas for API serialization
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, List

from pydantic import BaseModel, validator


# Shared properties
class FinancialRecordBase(BaseModel):
    amount: Decimal
    currency: str = "USD"
    record_type: str  # INCOME or EXPENSE
    category: str
    subcategory: Optional[str] = None
    title: str
    description: Optional[str] = None
    user_id: Optional[int] = None
    payment_id: Optional[int] = None
    transaction_date: datetime
    due_date: Optional[datetime] = None
    is_recurring: bool = False
    recurring_frequency: Optional[str] = None
    is_taxable: bool = True
    tax_rate: Optional[Decimal] = None
    tax_amount: Optional[Decimal] = None
    reference_number: Optional[str] = None
    external_reference: Optional[str] = None
    attachment_urls: Optional[str] = None
    notes: Optional[str] = None

    @validator("amount")
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError("Amount must be greater than 0")
        return v

    @validator("record_type")
    def validate_record_type(cls, v):
        if v not in ["INCOME", "EXPENSE"]:
            raise ValueError("Record type must be INCOME or EXPENSE")
        return v

    @validator("tax_rate")
    def validate_tax_rate(cls, v):
        if v is not None and (v < 0 or v > 100):
            raise ValueError("Tax rate must be between 0 and 100")
        return v


# Properties to receive via API on creation
class FinancialRecordCreate(FinancialRecordBase):
    record_number: Optional[str] = None  # Will be auto-generated if not provided


# Properties to receive via API on update
class FinancialRecordUpdate(BaseModel):
    amount: Optional[Decimal] = None
    category: Optional[str] = None
    subcategory: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    transaction_date: Optional[datetime] = None
    due_date: Optional[datetime] = None
    status: Optional[str] = None
    is_recurring: Optional[bool] = None
    recurring_frequency: Optional[str] = None
    is_taxable: Optional[bool] = None
    tax_rate: Optional[Decimal] = None
    tax_amount: Optional[Decimal] = None
    reference_number: Optional[str] = None
    external_reference: Optional[str] = None
    attachment_urls: Optional[str] = None
    notes: Optional[str] = None


class FinancialRecordInDBBase(FinancialRecordBase):
    id: Optional[int] = None
    record_number: str
    created_by_id: int
    status: str
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    approved_at: Optional[datetime] = None

    class Config:
        orm_mode = True


# Additional properties to return via API
class FinancialRecordResponse(FinancialRecordInDBBase):
    net_amount: Decimal = Decimal('0')
    is_income: bool = False
    is_expense: bool = False

    @validator("net_amount", pre=True, always=True)
    def set_net_amount(cls, v, values):
        amount = values.get("amount", Decimal('0'))
        tax_amount = values.get("tax_amount", Decimal('0'))
        return amount - (tax_amount or Decimal('0'))

    @validator("is_income", pre=True, always=True)
    def set_is_income(cls, v, values):
        return values.get("record_type") == "INCOME"

    @validator("is_expense", pre=True, always=True)
    def set_is_expense(cls, v, values):
        return values.get("record_type") == "EXPENSE"


# Additional properties stored in DB
class FinancialRecordInDB(FinancialRecordInDBBase):
    pass


# Financial record filter parameters
class FinancialRecordFilter(BaseModel):
    record_type: Optional[str] = None
    category: Optional[str] = None
    status: Optional[str] = None
    user_id: Optional[int] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    amount_min: Optional[Decimal] = None
    amount_max: Optional[Decimal] = None
    is_recurring: Optional[bool] = None


# Financial statistics
class FinancialStats(BaseModel):
    total_income: Decimal
    total_expenses: Decimal
    net_profit: Decimal
    pending_income: Decimal
    pending_expenses: Decimal
    approved_income: Decimal
    approved_expenses: Decimal
    tax_amount: Decimal


# Financial dashboard data
class FinancialDashboard(BaseModel):
    stats: FinancialStats
    recent_transactions: List[FinancialRecordResponse]
    income_by_category: List[dict]
    expenses_by_category: List[dict]
    monthly_trend: List[dict]


# Teacher salary record
class TeacherSalaryCreate(BaseModel):
    teacher_id: int
    salary_amount: Decimal
    bonus_amount: Optional[Decimal] = None
    deductions: Optional[Decimal] = None
    pay_period_start: datetime
    pay_period_end: datetime
    notes: Optional[str] = None

    @validator("salary_amount")
    def validate_salary_amount(cls, v):
        if v <= 0:
            raise ValueError("Salary amount must be greater than 0")
        return v


class TeacherSalaryResponse(BaseModel):
    id: int
    teacher_id: int
    teacher_name: str
    salary_amount: Decimal
    bonus_amount: Optional[Decimal] = None
    deductions: Optional[Decimal] = None
    net_amount: Decimal
    pay_period_start: datetime
    pay_period_end: datetime
    status: str
    created_at: datetime
    notes: Optional[str] = None

    class Config:
        orm_mode = True
