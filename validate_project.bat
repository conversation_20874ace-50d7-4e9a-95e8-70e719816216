@echo off
echo 🔍 Validating Innovative Centre Platform Project Structure
echo ============================================================

set passed=0
set total=0

echo.
echo 📁 PROJECT STRUCTURE
echo ------------------------------

call :check_dir "backend" "Backend directory"
call :check_dir "frontend" "Frontend directory"
call :check_dir "docs" "Documentation directory"
call :check_dir "scripts" "Scripts directory"
call :check_dir "backend\admin-service" "Admin service directory"
call :check_dir "frontend\admin-portal" "Admin portal directory"

echo.
echo 🐍 BACKEND FILES
echo ------------------------------

call :check_file "backend\admin-service\main.py" "Main FastAPI application"
call :check_file "backend\admin-service\requirements.txt" "Python dependencies"
call :check_file "backend\admin-service\Dockerfile" "Docker configuration"
call :check_file "backend\admin-service\Dockerfile.prod" "Production Docker config"
call :check_file "backend\admin-service\app\__init__.py" "App package"
call :check_file "backend\admin-service\app\api\v1\api.py" "API router"
call :check_file "backend\admin-service\app\core\config.py" "Configuration"
call :check_file "backend\admin-service\tests\conftest.py" "Test configuration"
call :check_file "backend\admin-service\run_tests.py" "Test runner"

echo.
echo ⚛️ FRONTEND FILES
echo ------------------------------

call :check_file "frontend\admin-portal\package.json" "Package configuration"
call :check_file "frontend\admin-portal\next.config.js" "Next.js configuration"
call :check_file "frontend\admin-portal\tailwind.config.js" "Tailwind configuration"
call :check_file "frontend\admin-portal\tsconfig.json" "TypeScript configuration"
call :check_file "frontend\admin-portal\Dockerfile.prod" "Production Docker config"

echo.
echo 🐳 DOCKER FILES
echo ------------------------------

call :check_file "docker-compose.yml" "Development Docker Compose"
call :check_file "docker-compose.prod.yml" "Production Docker Compose"
call :check_file ".env.prod.example" "Production environment template"

echo.
echo 📚 DOCUMENTATION
echo ------------------------------

call :check_file "README.md" "Project README"
call :check_file "PROJECT_COMPLETION_SUMMARY.md" "Completion summary"
call :check_file "TESTING_GUIDE.md" "Testing guide"
call :check_file "DEPLOYMENT_CHECKLIST.md" "Deployment checklist"
call :check_file "docs\API_DOCUMENTATION.md" "API documentation"
call :check_file "docs\USER_GUIDE.md" "User guide"
call :check_file "docs\DEPLOYMENT_GUIDE.md" "Deployment guide"

echo.
echo 📜 SCRIPTS
echo ------------------------------

call :check_file "scripts\deploy-production.sh" "Production deployment script"
call :check_file "setup-project.ps1" "Project setup script"

echo.
echo ============================================================
echo 📊 VALIDATION SUMMARY
echo ============================================================

set /a percentage=(%passed% * 100) / %total%

echo ✅ Passed: %passed%
set /a failed=%total% - %passed%
echo ❌ Failed: %failed%
echo 📊 Total: %total%
echo 🎯 Success Rate: %percentage%%%

if %percentage% GEQ 90 (
    echo.
    echo 🎉 EXCELLENT! Project structure is complete and ready for testing!
    exit /b 0
) else if %percentage% GEQ 75 (
    echo.
    echo ✅ GOOD! Project structure is mostly complete with minor issues.
    exit /b 0
) else if %percentage% GEQ 50 (
    echo.
    echo ⚠️ WARNING! Project has significant missing components.
    exit /b 1
) else (
    echo.
    echo ❌ ERROR! Project structure is incomplete.
    exit /b 1
)

:check_file
set /a total+=1
if exist "%~1" (
    echo ✅ %~2: %~1
    set /a passed+=1
) else (
    echo ❌ %~2: %~1 - NOT FOUND
)
goto :eof

:check_dir
set /a total+=1
if exist "%~1" (
    echo ✅ %~2: %~1
    set /a passed+=1
) else (
    echo ❌ %~2: %~1 - NOT FOUND
)
goto :eof
