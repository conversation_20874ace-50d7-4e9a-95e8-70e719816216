"""
Role service for role and permission management
"""

from typing import List, Optional

from sqlalchemy.orm import Session
from sqlalchemy import func

from app.models.role import Role
from app.models.user import User
from app.schemas.role import RoleCreate, RoleUpdate
from app.services.base_service import BaseService
from app.utils.audit import AuditLogger, AuditActions, AuditResourceTypes


class RoleService(BaseService[Role, RoleCreate, RoleUpdate]):
    def create(self, db: Session, *, obj_in: RoleCreate, created_by: User) -> Role:
        """Create a new role"""
        db_obj = Role(
            name=obj_in.name,
            display_name=obj_in.display_name,
            description=obj_in.description,
            permissions=obj_in.permissions,
            is_active=obj_in.is_active,
            is_system=False  # User-created roles are never system roles
        )
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        # Log audit trail
        AuditLogger.log_create(
            db=db,
            user=created_by,
            resource_type=AuditResourceTypes.ROLE,
            resource_id=str(db_obj.id),
            new_values=obj_in.dict(),
            description=f"Created role {obj_in.name}"
        )
        
        return db_obj

    def get_by_name(self, db: Session, *, name: str) -> Optional[Role]:
        """Get role by name"""
        return db.query(Role).filter(Role.name == name).first()

    def get_active_roles(self, db: Session) -> List[Role]:
        """Get all active roles"""
        return db.query(Role).filter(Role.is_active == True).all()

    def get_system_roles(self, db: Session) -> List[Role]:
        """Get all system roles"""
        return db.query(Role).filter(Role.is_system == True).all()

    def get_user_roles(self, db: Session) -> List[Role]:
        """Get all user-created roles"""
        return db.query(Role).filter(Role.is_system == False).all()

    def get_users_with_role(self, db: Session, *, role_id: int) -> List[User]:
        """Get all users assigned to a specific role"""
        return db.query(User).filter(User.role_id == role_id).all()

    def get_role_user_count(self, db: Session, *, role_id: int) -> int:
        """Get count of users assigned to a role"""
        return db.query(func.count(User.id)).filter(User.role_id == role_id).scalar()

    def add_permission(self, db: Session, *, role: Role, permission: str) -> Role:
        """Add a permission to a role"""
        if permission not in role.permissions:
            old_permissions = role.permissions.copy()
            role.permissions.append(permission)
            
            # Mark the permissions field as modified for SQLAlchemy
            from sqlalchemy.orm.attributes import flag_modified
            flag_modified(role, "permissions")
            
            db.add(role)
            db.commit()
            db.refresh(role)
            
            # Log audit trail
            AuditLogger.log_action(
                db=db,
                user=None,  # System action
                action=AuditActions.PERMISSION_GRANT,
                resource_type=AuditResourceTypes.ROLE,
                resource_id=str(role.id),
                old_values={"permissions": old_permissions},
                new_values={"permissions": role.permissions},
                description=f"Added permission {permission} to role {role.name}"
            )
        
        return role

    def remove_permission(self, db: Session, *, role: Role, permission: str) -> Role:
        """Remove a permission from a role"""
        if permission in role.permissions:
            old_permissions = role.permissions.copy()
            role.permissions.remove(permission)
            
            # Mark the permissions field as modified for SQLAlchemy
            from sqlalchemy.orm.attributes import flag_modified
            flag_modified(role, "permissions")
            
            db.add(role)
            db.commit()
            db.refresh(role)
            
            # Log audit trail
            AuditLogger.log_action(
                db=db,
                user=None,  # System action
                action=AuditActions.PERMISSION_REVOKE,
                resource_type=AuditResourceTypes.ROLE,
                resource_id=str(role.id),
                old_values={"permissions": old_permissions},
                new_values={"permissions": role.permissions},
                description=f"Removed permission {permission} from role {role.name}"
            )
        
        return role

    def assign_role_to_user(self, db: Session, *, user: User, role: Role, assigned_by: User) -> User:
        """Assign a role to a user"""
        old_role_id = user.role_id
        user.role_id = role.id
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        # Log audit trail
        AuditLogger.log_action(
            db=db,
            user=assigned_by,
            action=AuditActions.UPDATE,
            resource_type=AuditResourceTypes.USER,
            resource_id=str(user.id),
            old_values={"role_id": old_role_id},
            new_values={"role_id": role.id},
            description=f"Assigned role {role.name} to user {user.email}"
        )
        
        return user

    def remove_role_from_user(self, db: Session, *, user: User, removed_by: User) -> User:
        """Remove role from a user"""
        old_role_id = user.role_id
        user.role_id = None
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        # Log audit trail
        AuditLogger.log_action(
            db=db,
            user=removed_by,
            action=AuditActions.UPDATE,
            resource_type=AuditResourceTypes.USER,
            resource_id=str(user.id),
            old_values={"role_id": old_role_id},
            new_values={"role_id": None},
            description=f"Removed role from user {user.email}"
        )
        
        return user

    def check_user_permission(self, db: Session, *, user: User, permission: str) -> bool:
        """Check if a user has a specific permission"""
        if user.is_superuser:
            return True
        
        if not user.role_id:
            return False
        
        role = self.get(db, id=user.role_id)
        if not role or not role.is_active:
            return False
        
        return role.has_permission(permission)

    def get_user_permissions(self, db: Session, *, user: User) -> List[str]:
        """Get all permissions for a user"""
        if user.is_superuser:
            from app.models.role import Permissions
            return Permissions.get_all_permissions()
        
        if not user.role_id:
            return []
        
        role = self.get(db, id=user.role_id)
        if not role or not role.is_active:
            return []
        
        return role.permissions


# Create role service instance
role_service = RoleService(Role)
