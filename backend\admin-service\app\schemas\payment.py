"""
Payment schemas for API serialization
"""

from datetime import datetime
from decimal import Decimal
from typing import Optional, Dict, Any

from pydantic import BaseModel, validator


# Shared properties
class PaymentBase(BaseModel):
    amount: Decimal
    currency: str = "USD"
    payment_method: str
    payment_gateway: Optional[str] = None
    purpose: str
    description: Optional[str] = None
    user_id: Optional[int] = None
    due_date: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

    @validator("amount")
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError("Amount must be greater than 0")
        return v

    @validator("currency")
    def validate_currency(cls, v):
        if len(v) != 3:
            raise ValueError("Currency must be a 3-letter code")
        return v.upper()


# Properties to receive via API on creation
class PaymentCreate(PaymentBase):
    payment_id: Optional[str] = None  # Will be auto-generated if not provided


# Properties to receive via API on update
class PaymentUpdate(BaseModel):
    status: Optional[str] = None
    transaction_id: Optional[str] = None
    gateway_response: Optional[Dict[str, Any]] = None
    processed_at: Optional[datetime] = None
    processed_by_id: Optional[int] = None
    refunded_amount: Optional[Decimal] = None
    refund_reason: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class PaymentInDBBase(PaymentBase):
    id: Optional[int] = None
    payment_id: str
    transaction_id: Optional[str] = None
    status: str
    processed_by_id: Optional[int] = None
    gateway_response: Optional[Dict[str, Any]] = None
    refunded_amount: Optional[Decimal] = None
    refund_reason: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    processed_at: Optional[datetime] = None

    class Config:
        orm_mode = True


# Additional properties to return via API
class PaymentResponse(PaymentInDBBase):
    is_completed: bool = False
    is_refundable: bool = False
    remaining_amount: Decimal = Decimal('0')

    @validator("is_completed", pre=True, always=True)
    def set_is_completed(cls, v, values):
        return values.get("status") == "COMPLETED"

    @validator("is_refundable", pre=True, always=True)
    def set_is_refundable(cls, v, values):
        status = values.get("status")
        amount = values.get("amount", Decimal('0'))
        refunded = values.get("refunded_amount", Decimal('0'))
        return status == "COMPLETED" and refunded < amount

    @validator("remaining_amount", pre=True, always=True)
    def set_remaining_amount(cls, v, values):
        amount = values.get("amount", Decimal('0'))
        refunded = values.get("refunded_amount", Decimal('0'))
        return amount - refunded


# Additional properties stored in DB
class PaymentInDB(PaymentInDBBase):
    pass


# Payment processing request
class PaymentProcessRequest(BaseModel):
    payment_id: str
    transaction_id: Optional[str] = None
    gateway_response: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None


# Payment refund request
class PaymentRefundRequest(BaseModel):
    payment_id: str
    refund_amount: Decimal
    refund_reason: str
    notes: Optional[str] = None

    @validator("refund_amount")
    def validate_refund_amount(cls, v):
        if v <= 0:
            raise ValueError("Refund amount must be greater than 0")
        return v


# Payment search/filter parameters
class PaymentFilter(BaseModel):
    status: Optional[str] = None
    payment_method: Optional[str] = None
    user_id: Optional[int] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    amount_min: Optional[Decimal] = None
    amount_max: Optional[Decimal] = None
    purpose: Optional[str] = None


# Payment statistics
class PaymentStats(BaseModel):
    total_payments: int
    total_amount: Decimal
    completed_payments: int
    completed_amount: Decimal
    pending_payments: int
    pending_amount: Decimal
    failed_payments: int
    refunded_amount: Decimal
