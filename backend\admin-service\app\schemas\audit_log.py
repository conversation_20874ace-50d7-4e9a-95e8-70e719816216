"""
Audit log schemas for API serialization
"""

from datetime import datetime
from typing import Optional, Dict, Any, List

from pydantic import BaseModel


# Shared properties
class AuditLogBase(BaseModel):
    action: str
    resource_type: str
    resource_id: Optional[str] = None
    endpoint: Optional[str] = None
    method: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    old_values: Optional[Dict[str, Any]] = None
    new_values: Optional[Dict[str, Any]] = None
    description: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    status: str = "SUCCESS"
    error_message: Optional[str] = None


class AuditLogInDBBase(AuditLogBase):
    id: Optional[int] = None
    user_id: Optional[int] = None
    created_at: Optional[datetime] = None

    class Config:
        orm_mode = True


# Additional properties to return via API
class AuditLogResponse(AuditLogInDBBase):
    user_email: Optional[str] = None
    user_name: Optional[str] = None


# Audit log filter parameters
class AuditLogFilter(BaseModel):
    user_id: Optional[int] = None
    action: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    status: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    ip_address: Optional[str] = None
    search: Optional[str] = None


# Audit statistics
class AuditStats(BaseModel):
    total_logs: int
    successful_actions: int
    failed_actions: int
    unique_users: int
    unique_ips: int
    top_actions: List[Dict[str, Any]]
    top_resources: List[Dict[str, Any]]
    recent_activity: List[AuditLogResponse]


# Security report
class SecurityReport(BaseModel):
    failed_logins: int
    suspicious_activities: int
    admin_actions: int
    permission_changes: int
    config_changes: int
    unusual_access_patterns: List[Dict[str, Any]]
    ip_analysis: List[Dict[str, Any]]


# Activity summary
class ActivitySummary(BaseModel):
    date: str
    total_actions: int
    successful_actions: int
    failed_actions: int
    unique_users: int
    top_action: Optional[str] = None


# User activity report
class UserActivityReport(BaseModel):
    user_id: int
    user_email: str
    user_name: str
    total_actions: int
    successful_actions: int
    failed_actions: int
    last_activity: Optional[datetime] = None
    top_actions: List[Dict[str, Any]]
    recent_activities: List[AuditLogResponse]


# Export request
class AuditExportRequest(BaseModel):
    filters: AuditLogFilter
    format: str = "csv"  # csv, json, xlsx
    include_sensitive: bool = False

    class Config:
        schema_extra = {
            "example": {
                "filters": {
                    "date_from": "2024-01-01T00:00:00",
                    "date_to": "2024-12-31T23:59:59",
                    "action": "LOGIN"
                },
                "format": "csv",
                "include_sensitive": False
            }
        }
