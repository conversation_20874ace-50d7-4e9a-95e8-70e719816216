"""
Performance optimization utilities
"""

import time
import asyncio
from typing import Any, Callable, Dict, List, Optional
from functools import wraps
from contextlib import asynccontextmanager
from sqlalchemy import text
from sqlalchemy.orm import Session
from fastapi import Request, Response
import structlog

logger = structlog.get_logger()


class PerformanceMonitor:
    """Monitor and log performance metrics."""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
    
    def record_timing(self, operation: str, duration: float):
        """Record timing for an operation."""
        if operation not in self.metrics:
            self.metrics[operation] = []
        self.metrics[operation].append(duration)
    
    def get_average_timing(self, operation: str) -> Optional[float]:
        """Get average timing for an operation."""
        if operation not in self.metrics or not self.metrics[operation]:
            return None
        return sum(self.metrics[operation]) / len(self.metrics[operation])
    
    def get_metrics_summary(self) -> Dict[str, Dict[str, float]]:
        """Get summary of all metrics."""
        summary = {}
        for operation, timings in self.metrics.items():
            if timings:
                summary[operation] = {
                    "count": len(timings),
                    "average": sum(timings) / len(timings),
                    "min": min(timings),
                    "max": max(timings),
                    "total": sum(timings)
                }
        return summary


# Global performance monitor
performance_monitor = PerformanceMonitor()


def timing_decorator(operation_name: str = None):
    """Decorator to measure function execution time."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                op_name = operation_name or f"{func.__module__}.{func.__name__}"
                performance_monitor.record_timing(op_name, duration)
                
                if duration > 1.0:  # Log slow operations
                    logger.warning(
                        "Slow operation detected",
                        operation=op_name,
                        duration=duration,
                        args_count=len(args),
                        kwargs_count=len(kwargs)
                    )
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                op_name = operation_name or f"{func.__module__}.{func.__name__}"
                performance_monitor.record_timing(op_name, duration)
                
                if duration > 1.0:  # Log slow operations
                    logger.warning(
                        "Slow operation detected",
                        operation=op_name,
                        duration=duration,
                        args_count=len(args),
                        kwargs_count=len(kwargs)
                    )
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


@asynccontextmanager
async def performance_context(operation_name: str):
    """Context manager for measuring performance."""
    start_time = time.time()
    try:
        yield
    finally:
        duration = time.time() - start_time
        performance_monitor.record_timing(operation_name, duration)


class DatabaseOptimizer:
    """Database query optimization utilities."""
    
    @staticmethod
    def add_query_hints(query: str, hints: List[str]) -> str:
        """Add query hints for optimization."""
        if not hints:
            return query
        
        hint_string = " ".join(f"/*+ {hint} */" for hint in hints)
        return f"{hint_string} {query}"
    
    @staticmethod
    def analyze_query_performance(db: Session, query: str) -> Dict[str, Any]:
        """Analyze query performance using EXPLAIN."""
        try:
            explain_query = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"
            result = db.execute(text(explain_query)).fetchone()
            return result[0] if result else {}
        except Exception as e:
            logger.error("Failed to analyze query", error=str(e))
            return {}
    
    @staticmethod
    def suggest_indexes(db: Session, table_name: str) -> List[str]:
        """Suggest indexes for a table based on query patterns."""
        # This is a simplified version - in production, you'd analyze query logs
        suggestions = []
        
        try:
            # Check for missing indexes on foreign keys
            fk_query = text("""
                SELECT column_name 
                FROM information_schema.key_column_usage 
                WHERE table_name = :table_name 
                AND referenced_table_name IS NOT NULL
            """)
            
            result = db.execute(fk_query, {"table_name": table_name}).fetchall()
            for row in result:
                suggestions.append(f"CREATE INDEX idx_{table_name}_{row[0]} ON {table_name}({row[0]})")
            
        except Exception as e:
            logger.error("Failed to suggest indexes", error=str(e))
        
        return suggestions


class ResponseOptimizer:
    """Response optimization utilities."""
    
    @staticmethod
    def compress_response(data: Any, compression_threshold: int = 1024) -> Any:
        """Compress response data if it exceeds threshold."""
        # This would implement actual compression logic
        # For now, it's a placeholder
        return data
    
    @staticmethod
    def paginate_results(
        results: List[Any], 
        page: int = 1, 
        page_size: int = 50
    ) -> Dict[str, Any]:
        """Paginate results for better performance."""
        total = len(results)
        start = (page - 1) * page_size
        end = start + page_size
        
        return {
            "items": results[start:end],
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total": total,
                "pages": (total + page_size - 1) // page_size,
                "has_next": end < total,
                "has_prev": page > 1
            }
        }


class MemoryOptimizer:
    """Memory usage optimization utilities."""
    
    @staticmethod
    def batch_process(items: List[Any], batch_size: int = 100):
        """Process items in batches to reduce memory usage."""
        for i in range(0, len(items), batch_size):
            yield items[i:i + batch_size]
    
    @staticmethod
    async def async_batch_process(items: List[Any], batch_size: int = 100):
        """Async version of batch processing."""
        for i in range(0, len(items), batch_size):
            yield items[i:i + batch_size]
            await asyncio.sleep(0)  # Allow other tasks to run


# Middleware for performance monitoring
async def performance_middleware(request: Request, call_next):
    """Middleware to monitor request performance."""
    start_time = time.time()
    
    # Add request ID for tracking
    request_id = request.headers.get("X-Request-ID", f"req_{int(time.time() * 1000)}")
    
    response = await call_next(request)
    
    duration = time.time() - start_time
    
    # Log performance metrics
    logger.info(
        "Request completed",
        request_id=request_id,
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        duration=duration
    )
    
    # Add performance headers
    response.headers["X-Request-ID"] = request_id
    response.headers["X-Response-Time"] = f"{duration:.3f}s"
    
    # Record metrics
    operation_name = f"{request.method}:{request.url.path}"
    performance_monitor.record_timing(operation_name, duration)
    
    return response


# Query optimization helpers
class QueryOptimizer:
    """SQL query optimization helpers."""
    
    @staticmethod
    def build_efficient_filter(filters: Dict[str, Any]) -> str:
        """Build efficient WHERE clause from filters."""
        conditions = []
        for key, value in filters.items():
            if value is not None:
                if isinstance(value, list):
                    conditions.append(f"{key} IN ({','.join(['%s'] * len(value))})")
                else:
                    conditions.append(f"{key} = %s")
        
        return " AND ".join(conditions) if conditions else "1=1"
    
    @staticmethod
    def optimize_join_order(tables: List[str], relationships: Dict[str, str]) -> List[str]:
        """Optimize JOIN order for better performance."""
        # Simplified optimization - in production, use query planner statistics
        return sorted(tables, key=lambda t: len(relationships.get(t, "")))


# Connection pooling optimization
class ConnectionPoolOptimizer:
    """Database connection pool optimization."""
    
    @staticmethod
    def calculate_optimal_pool_size(concurrent_users: int, avg_request_time: float) -> int:
        """Calculate optimal connection pool size."""
        # Rule of thumb: pool_size = concurrent_users * avg_request_time * safety_factor
        safety_factor = 1.2
        optimal_size = int(concurrent_users * avg_request_time * safety_factor)
        
        # Ensure reasonable bounds
        return max(5, min(optimal_size, 50))
    
    @staticmethod
    def get_pool_health_metrics(pool) -> Dict[str, Any]:
        """Get connection pool health metrics."""
        return {
            "size": getattr(pool, "size", 0),
            "checked_in": getattr(pool, "checkedin", 0),
            "checked_out": getattr(pool, "checkedout", 0),
            "overflow": getattr(pool, "overflow", 0),
            "invalid": getattr(pool, "invalid", 0)
        }
