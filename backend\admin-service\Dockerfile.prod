# Multi-stage production Dockerfile for Admin Service
ARG PYTHON_VERSION=3.13.5

# Build stage
FROM python:${PYTHON_VERSION}-slim as builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies for building
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        libpq-dev \
        gcc \
        g++ \
        curl \
    && rm -rf /var/lib/apt/lists/*

# Create and activate virtual environment
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:${PYTHON_VERSION}-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app
ENV PATH="/opt/venv/bin:$PATH"

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install runtime dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        postgresql-client \
        curl \
        ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy virtual environment from builder stage
COPY --from=builder /opt/venv /opt/venv

# Set work directory
WORKDIR /app

# Copy application code
COPY --chown=appuser:appuser . .

# Create necessary directories
RUN mkdir -p uploads logs \
    && chown -R appuser:appuser /app

# Health check script
COPY --chown=appuser:appuser <<EOF /app/healthcheck.py
#!/usr/bin/env python3
import sys
import requests
import os

def health_check():
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("Health check passed")
            sys.exit(0)
        else:
            print(f"Health check failed with status {response.status_code}")
            sys.exit(1)
    except Exception as e:
        print(f"Health check failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    health_check()
EOF

RUN chmod +x /app/healthcheck.py

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD python /app/healthcheck.py

# Production startup script
COPY --chown=appuser:appuser <<EOF /app/start.sh
#!/bin/bash
set -e

echo "Starting Innovative Centre Admin Service..."

# Wait for database
echo "Waiting for database..."
while ! pg_isready -h \${DATABASE_HOST:-db} -p \${DATABASE_PORT:-5432} -U \${POSTGRES_USER:-postgres}; do
    echo "Database is unavailable - sleeping"
    sleep 2
done
echo "Database is up!"

# Run migrations
echo "Running database migrations..."
python -m alembic upgrade head

# Create initial data if needed
if [ "\${CREATE_INITIAL_DATA:-false}" = "true" ]; then
    echo "Creating initial data..."
    python create_initial_data.py
fi

# Start the application
echo "Starting application..."
exec uvicorn main:app \
    --host 0.0.0.0 \
    --port 8000 \
    --workers \${WORKERS:-4} \
    --worker-class uvicorn.workers.UvicornWorker \
    --access-log \
    --log-level \${LOG_LEVEL:-info} \
    --no-use-colors
EOF

RUN chmod +x /app/start.sh

# Default command
CMD ["/app/start.sh"]
