"""
System configuration schemas for API serialization
"""

from datetime import datetime
from typing import Optional, Any, Dict

from pydantic import BaseModel, validator


# Shared properties
class SystemConfigBase(BaseModel):
    key: str
    category: str
    name: str
    description: Optional[str] = None
    data_type: str
    value: Optional[Any] = None
    default_value: Optional[Any] = None
    validation_rules: Optional[Dict[str, Any]] = None
    is_active: bool = True
    is_sensitive: bool = False
    required_permission: Optional[str] = None

    @validator("key")
    def validate_key(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Configuration key cannot be empty")
        # Ensure key follows dot notation pattern
        if not all(part.isalnum() or part in ['_', '-'] for part in v.replace('.', '').replace('_', '').replace('-', '')):
            raise ValueError("Configuration key can only contain alphanumeric characters, dots, underscores, and hyphens")
        return v.lower()

    @validator("data_type")
    def validate_data_type(cls, v):
        valid_types = ["string", "number", "boolean", "json", "array"]
        if v not in valid_types:
            raise ValueError(f"Data type must be one of: {', '.join(valid_types)}")
        return v


# Properties to receive via API on creation
class SystemConfigCreate(SystemConfigBase):
    pass


# Properties to receive via API on update
class SystemConfigUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    value: Optional[Any] = None
    validation_rules: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    is_sensitive: Optional[bool] = None
    required_permission: Optional[str] = None


class SystemConfigInDBBase(SystemConfigBase):
    id: Optional[int] = None
    is_system: bool = False
    created_by_id: int
    updated_by_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


# Additional properties to return via API
class SystemConfigResponse(SystemConfigInDBBase):
    typed_value: Optional[Any] = None

    @validator("typed_value", pre=True, always=True)
    def set_typed_value(cls, v, values):
        # This will be set by the service layer
        return v


# Additional properties stored in DB
class SystemConfigInDB(SystemConfigInDBBase):
    pass


# Configuration value update
class ConfigValueUpdate(BaseModel):
    value: Any

    @validator("value")
    def validate_value(cls, v):
        # Basic validation - more specific validation happens in service layer
        return v


# Configuration filter
class SystemConfigFilter(BaseModel):
    category: Optional[str] = None
    is_active: Optional[bool] = None
    is_system: Optional[bool] = None
    search: Optional[str] = None


# Configuration category info
class ConfigCategoryInfo(BaseModel):
    category: str
    name: str
    description: Optional[str] = None
    config_count: int = 0


# Configuration validation result
class ConfigValidationResult(BaseModel):
    is_valid: bool
    errors: list = []
    warnings: list = []


# Bulk configuration update
class BulkConfigUpdate(BaseModel):
    configs: Dict[str, Any]  # key -> value mapping

    @validator("configs")
    def validate_configs(cls, v):
        if not v:
            raise ValueError("At least one configuration must be provided")
        return v


# Configuration backup/restore
class ConfigBackup(BaseModel):
    timestamp: datetime
    configs: Dict[str, Any]
    created_by: str


class ConfigRestore(BaseModel):
    backup_data: Dict[str, Any]
    restore_system_configs: bool = False

    @validator("backup_data")
    def validate_backup_data(cls, v):
        if not v:
            raise ValueError("Backup data cannot be empty")
        return v
