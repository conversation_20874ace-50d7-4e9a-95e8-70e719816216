"""
Security hardening utilities and middleware
"""

import re
import time
import hashlib
import secrets
from typing import Dict, List, Optional, Set
from datetime import datetime, timedelta
from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import structlog
from app.core.cache import cache_manager

logger = structlog.get_logger()


class RateLimiter:
    """Rate limiting implementation."""
    
    def __init__(self):
        self.requests: Dict[str, List[float]] = {}
        self.blocked_ips: Set[str] = set()
    
    async def is_allowed(
        self, 
        identifier: str, 
        max_requests: int = 100, 
        window_seconds: int = 3600
    ) -> bool:
        """Check if request is allowed based on rate limits."""
        current_time = time.time()
        
        # Check if IP is blocked
        if identifier in self.blocked_ips:
            return False
        
        # Get request history from cache
        cache_key = f"rate_limit:{identifier}"
        request_times = await cache_manager.get(cache_key) or []
        
        # Remove old requests outside the window
        request_times = [t for t in request_times if current_time - t < window_seconds]
        
        # Check if limit exceeded
        if len(request_times) >= max_requests:
            # Block IP if severely exceeding limits
            if len(request_times) > max_requests * 2:
                self.blocked_ips.add(identifier)
                logger.warning(
                    "IP blocked for excessive requests",
                    ip=identifier,
                    request_count=len(request_times)
                )
            return False
        
        # Add current request
        request_times.append(current_time)
        
        # Update cache
        await cache_manager.set(cache_key, request_times, window_seconds)
        
        return True
    
    async def get_remaining_requests(
        self, 
        identifier: str, 
        max_requests: int = 100, 
        window_seconds: int = 3600
    ) -> int:
        """Get remaining requests for identifier."""
        cache_key = f"rate_limit:{identifier}"
        request_times = await cache_manager.get(cache_key) or []
        current_time = time.time()
        
        # Remove old requests
        recent_requests = [t for t in request_times if current_time - t < window_seconds]
        
        return max(0, max_requests - len(recent_requests))


class InputSanitizer:
    """Input sanitization and validation."""
    
    # Dangerous patterns to detect
    SQL_INJECTION_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
        r"(--|#|/\*|\*/)",
        r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
        r"(\bUNION\s+SELECT\b)",
    ]
    
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe[^>]*>",
        r"<object[^>]*>",
        r"<embed[^>]*>",
    ]
    
    PATH_TRAVERSAL_PATTERNS = [
        r"\.\./",
        r"\.\.\\",
        r"%2e%2e%2f",
        r"%2e%2e\\",
    ]
    
    @classmethod
    def detect_sql_injection(cls, input_string: str) -> bool:
        """Detect potential SQL injection attempts."""
        if not input_string:
            return False
        
        input_lower = input_string.lower()
        for pattern in cls.SQL_INJECTION_PATTERNS:
            if re.search(pattern, input_lower, re.IGNORECASE):
                return True
        return False
    
    @classmethod
    def detect_xss(cls, input_string: str) -> bool:
        """Detect potential XSS attempts."""
        if not input_string:
            return False
        
        for pattern in cls.XSS_PATTERNS:
            if re.search(pattern, input_string, re.IGNORECASE):
                return True
        return False
    
    @classmethod
    def detect_path_traversal(cls, input_string: str) -> bool:
        """Detect potential path traversal attempts."""
        if not input_string:
            return False
        
        for pattern in cls.PATH_TRAVERSAL_PATTERNS:
            if re.search(pattern, input_string, re.IGNORECASE):
                return True
        return False
    
    @classmethod
    def sanitize_string(cls, input_string: str, max_length: int = 1000) -> str:
        """Sanitize input string."""
        if not input_string:
            return ""
        
        # Truncate if too long
        if len(input_string) > max_length:
            input_string = input_string[:max_length]
        
        # Remove null bytes
        input_string = input_string.replace('\x00', '')
        
        # Remove control characters except newline and tab
        input_string = ''.join(char for char in input_string 
                              if ord(char) >= 32 or char in '\n\t')
        
        return input_string.strip()
    
    @classmethod
    def validate_email(cls, email: str) -> bool:
        """Validate email format."""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @classmethod
    def validate_phone(cls, phone: str) -> bool:
        """Validate phone number format."""
        # Remove all non-digit characters
        digits_only = re.sub(r'\D', '', phone)
        # Check if it's a reasonable length
        return 10 <= len(digits_only) <= 15


class SecurityHeaders:
    """Security headers management."""
    
    @staticmethod
    def get_security_headers() -> Dict[str, str]:
        """Get recommended security headers."""
        return {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "font-src 'self'; "
                "connect-src 'self'; "
                "frame-ancestors 'none';"
            ),
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Permissions-Policy": (
                "geolocation=(), "
                "microphone=(), "
                "camera=(), "
                "payment=(), "
                "usb=(), "
                "magnetometer=(), "
                "gyroscope=(), "
                "speaker=()"
            )
        }


class FailedLoginTracker:
    """Track and manage failed login attempts."""
    
    def __init__(self):
        self.max_attempts = 5
        self.lockout_duration = 900  # 15 minutes
    
    async def record_failed_attempt(self, identifier: str) -> None:
        """Record a failed login attempt."""
        cache_key = f"failed_login:{identifier}"
        
        # Get current attempts
        attempts = await cache_manager.get(cache_key) or []
        current_time = time.time()
        
        # Add current attempt
        attempts.append(current_time)
        
        # Store with expiration
        await cache_manager.set(cache_key, attempts, self.lockout_duration)
        
        # Log suspicious activity
        if len(attempts) >= self.max_attempts:
            logger.warning(
                "Multiple failed login attempts detected",
                identifier=identifier,
                attempt_count=len(attempts)
            )
    
    async def is_locked_out(self, identifier: str) -> bool:
        """Check if identifier is locked out."""
        cache_key = f"failed_login:{identifier}"
        attempts = await cache_manager.get(cache_key) or []
        
        current_time = time.time()
        
        # Remove old attempts
        recent_attempts = [
            attempt for attempt in attempts 
            if current_time - attempt < self.lockout_duration
        ]
        
        return len(recent_attempts) >= self.max_attempts
    
    async def clear_failed_attempts(self, identifier: str) -> None:
        """Clear failed attempts for identifier."""
        cache_key = f"failed_login:{identifier}"
        await cache_manager.delete(cache_key)
    
    async def get_lockout_time_remaining(self, identifier: str) -> int:
        """Get remaining lockout time in seconds."""
        cache_key = f"failed_login:{identifier}"
        attempts = await cache_manager.get(cache_key) or []
        
        if not attempts or len(attempts) < self.max_attempts:
            return 0
        
        current_time = time.time()
        oldest_relevant_attempt = current_time - self.lockout_duration
        
        # Find the first attempt that would cause lockout
        relevant_attempts = [a for a in attempts if a > oldest_relevant_attempt]
        
        if len(relevant_attempts) >= self.max_attempts:
            # Time remaining is based on the oldest relevant attempt
            return int(self.lockout_duration - (current_time - min(relevant_attempts)))
        
        return 0


class SessionSecurity:
    """Session security management."""
    
    @staticmethod
    def generate_session_token() -> str:
        """Generate a secure session token."""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def generate_csrf_token() -> str:
        """Generate a CSRF token."""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def hash_session_id(session_id: str, salt: str = None) -> str:
        """Hash session ID for storage."""
        if salt is None:
            salt = secrets.token_hex(16)
        
        combined = f"{session_id}{salt}"
        hashed = hashlib.sha256(combined.encode()).hexdigest()
        return f"{salt}:{hashed}"
    
    @staticmethod
    def verify_session_hash(session_id: str, stored_hash: str) -> bool:
        """Verify session ID against stored hash."""
        try:
            salt, expected_hash = stored_hash.split(':', 1)
            combined = f"{session_id}{salt}"
            actual_hash = hashlib.sha256(combined.encode()).hexdigest()
            return actual_hash == expected_hash
        except ValueError:
            return False


# Global instances
rate_limiter = RateLimiter()
failed_login_tracker = FailedLoginTracker()


# Security middleware
async def security_middleware(request: Request, call_next):
    """Security middleware for request processing."""
    # Get client IP
    client_ip = request.client.host
    if "X-Forwarded-For" in request.headers:
        client_ip = request.headers["X-Forwarded-For"].split(",")[0].strip()
    
    # Rate limiting
    if not await rate_limiter.is_allowed(client_ip):
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail="Rate limit exceeded"
        )
    
    # Input validation for query parameters
    for key, value in request.query_params.items():
        if InputSanitizer.detect_sql_injection(value):
            logger.warning(
                "SQL injection attempt detected",
                ip=client_ip,
                parameter=key,
                value=value
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid input detected"
            )
        
        if InputSanitizer.detect_xss(value):
            logger.warning(
                "XSS attempt detected",
                ip=client_ip,
                parameter=key,
                value=value
            )
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid input detected"
            )
    
    # Process request
    response = await call_next(request)
    
    # Add security headers
    for header, value in SecurityHeaders.get_security_headers().items():
        response.headers[header] = value
    
    return response
