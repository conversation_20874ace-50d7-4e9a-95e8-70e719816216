#!/bin/bash

# Production Deployment Script for Innovative Centre Platform
# This script handles the complete production deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="$PROJECT_ROOT/backups"
LOG_FILE="$PROJECT_ROOT/logs/deployment.log"

# Default values
ENVIRONMENT="production"
SKIP_BACKUP=false
SKIP_TESTS=false
FORCE_REBUILD=false
DRY_RUN=false

# Functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
    echo "[ERROR] $1" >> "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
    echo "[WARNING] $1" >> "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
    echo "[INFO] $1" >> "$LOG_FILE"
}

usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Production deployment script for Innovative Centre Platform

OPTIONS:
    -e, --environment ENV    Target environment (default: production)
    -s, --skip-backup       Skip database backup
    -t, --skip-tests        Skip running tests
    -f, --force-rebuild     Force rebuild of all containers
    -d, --dry-run          Show what would be done without executing
    -h, --help             Show this help message

EXAMPLES:
    $0                      # Standard production deployment
    $0 --skip-backup        # Deploy without backup
    $0 --force-rebuild      # Force rebuild all containers
    $0 --dry-run           # Preview deployment steps

EOF
}

check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker is not running"
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed"
        exit 1
    fi
    
    # Check if required files exist
    if [[ ! -f "$PROJECT_ROOT/.env.prod" ]]; then
        error "Production environment file (.env.prod) not found"
        exit 1
    fi
    
    if [[ ! -f "$PROJECT_ROOT/docker-compose.prod.yml" ]]; then
        error "Production Docker Compose file not found"
        exit 1
    fi
    
    log "Prerequisites check passed"
}

create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p "$BACKUP_DIR"
    mkdir -p "$PROJECT_ROOT/logs"
    mkdir -p "$PROJECT_ROOT/nginx/ssl"
    mkdir -p "$PROJECT_ROOT/monitoring"
    
    log "Directories created"
}

backup_database() {
    if [[ "$SKIP_BACKUP" == true ]]; then
        warning "Skipping database backup"
        return
    fi
    
    log "Creating database backup..."
    
    local backup_file="$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    if [[ "$DRY_RUN" == true ]]; then
        info "DRY RUN: Would create backup at $backup_file"
        return
    fi
    
    # Check if database container is running
    if docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" ps db | grep -q "Up"; then
        docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" exec -T db \
            pg_dump -U postgres innovative_centre > "$backup_file"
        
        # Compress backup
        gzip "$backup_file"
        
        log "Database backup created: ${backup_file}.gz"
    else
        warning "Database container not running, skipping backup"
    fi
}

run_tests() {
    if [[ "$SKIP_TESTS" == true ]]; then
        warning "Skipping tests"
        return
    fi
    
    log "Running tests..."
    
    if [[ "$DRY_RUN" == true ]]; then
        info "DRY RUN: Would run test suite"
        return
    fi
    
    # Run backend tests
    cd "$PROJECT_ROOT/backend/admin-service"
    if [[ -f "run_tests.py" ]]; then
        python run_tests.py
    else
        warning "Test runner not found, skipping tests"
    fi
    
    cd "$PROJECT_ROOT"
    log "Tests completed"
}

build_and_deploy() {
    log "Building and deploying application..."
    
    cd "$PROJECT_ROOT"
    
    # Load environment variables
    if [[ -f ".env.prod" ]]; then
        export $(cat .env.prod | grep -v '^#' | xargs)
    fi
    
    local compose_args=""
    if [[ "$FORCE_REBUILD" == true ]]; then
        compose_args="--build --force-recreate"
    fi
    
    if [[ "$DRY_RUN" == true ]]; then
        info "DRY RUN: Would execute: docker-compose -f docker-compose.prod.yml up -d $compose_args"
        return
    fi
    
    # Pull latest images
    docker-compose -f docker-compose.prod.yml pull
    
    # Build and start services
    docker-compose -f docker-compose.prod.yml up -d $compose_args
    
    log "Application deployed"
}

wait_for_services() {
    log "Waiting for services to be ready..."
    
    if [[ "$DRY_RUN" == true ]]; then
        info "DRY RUN: Would wait for services to be ready"
        return
    fi
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:8000/health &> /dev/null; then
            log "Backend service is ready"
            break
        fi
        
        info "Waiting for backend service... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        error "Backend service failed to start"
        exit 1
    fi
    
    # Check frontend
    attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:3004/api/health &> /dev/null; then
            log "Frontend service is ready"
            break
        fi
        
        info "Waiting for frontend service... (attempt $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        error "Frontend service failed to start"
        exit 1
    fi
}

run_migrations() {
    log "Running database migrations..."
    
    if [[ "$DRY_RUN" == true ]]; then
        info "DRY RUN: Would run database migrations"
        return
    fi
    
    docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" exec -T admin-service \
        python -m alembic upgrade head
    
    log "Database migrations completed"
}

cleanup_old_images() {
    log "Cleaning up old Docker images..."
    
    if [[ "$DRY_RUN" == true ]]; then
        info "DRY RUN: Would clean up old Docker images"
        return
    fi
    
    # Remove unused images
    docker image prune -f
    
    # Remove old backups (keep last 7 days)
    find "$BACKUP_DIR" -name "*.gz" -mtime +7 -delete
    
    log "Cleanup completed"
}

verify_deployment() {
    log "Verifying deployment..."
    
    if [[ "$DRY_RUN" == true ]]; then
        info "DRY RUN: Would verify deployment"
        return
    fi
    
    # Check service status
    local services=("db" "redis" "admin-service" "admin-portal" "nginx")
    
    for service in "${services[@]}"; do
        if docker-compose -f "$PROJECT_ROOT/docker-compose.prod.yml" ps "$service" | grep -q "Up"; then
            log "✓ $service is running"
        else
            error "✗ $service is not running"
            exit 1
        fi
    done
    
    # Test API endpoints
    if curl -f http://localhost:8000/health &> /dev/null; then
        log "✓ Backend API is responding"
    else
        error "✗ Backend API is not responding"
        exit 1
    fi
    
    if curl -f http://localhost:3004 &> /dev/null; then
        log "✓ Frontend is responding"
    else
        error "✗ Frontend is not responding"
        exit 1
    fi
    
    log "Deployment verification completed successfully"
}

main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--environment)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -s|--skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            -t|--skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            -f|--force-rebuild)
                FORCE_REBUILD=true
                shift
                ;;
            -d|--dry-run)
                DRY_RUN=true
                shift
                ;;
            -h|--help)
                usage
                exit 0
                ;;
            *)
                error "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Start deployment
    log "Starting production deployment for Innovative Centre Platform"
    log "Environment: $ENVIRONMENT"
    
    if [[ "$DRY_RUN" == true ]]; then
        warning "DRY RUN MODE - No actual changes will be made"
    fi
    
    # Execute deployment steps
    check_prerequisites
    create_directories
    backup_database
    run_tests
    build_and_deploy
    wait_for_services
    run_migrations
    cleanup_old_images
    verify_deployment
    
    log "🎉 Production deployment completed successfully!"
    log "Access the application at: http://localhost:3004"
    log "API documentation: http://localhost:8000/docs"
    
    if [[ "$DRY_RUN" == false ]]; then
        info "Deployment logs saved to: $LOG_FILE"
    fi
}

# Run main function
main "$@"
