"""
Financial management endpoints
"""

from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.user import User
from app.models.financial_record import FinancialRecordType, FinancialRecordStatus, IncomeCategory, ExpenseCategory
from app.schemas.financial_record import (
    FinancialRecordCreate, FinancialRecordResponse, FinancialRecordUpdate,
    FinancialRecordFilter, FinancialStats, FinancialDashboard
)
from app.services.user_service import user_service
from app.services.financial_service import financial_service

router = APIRouter()


@router.get("/", response_model=List[FinancialRecordResponse])
async def get_financial_records(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    record_type: str = Query(None, description="Filter by record type (INCOME/EXPENSE)"),
    category: str = Query(None, description="Filter by category"),
    status: str = Query(None, description="Filter by status"),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Retrieve financial records with optional filters
    """
    filters = FinancialRecordFilter(
        record_type=record_type,
        category=category,
        status=status
    )
    
    records = financial_service.get_filtered(
        db, filters=filters, skip=skip, limit=limit
    )
    return records


@router.post("/", response_model=FinancialRecordResponse)
async def create_financial_record(
    *,
    db: Session = Depends(get_db),
    record_in: FinancialRecordCreate,
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Create new financial record
    """
    record = financial_service.create(db, obj_in=record_in, created_by=current_user)
    return record


@router.get("/{record_id}", response_model=FinancialRecordResponse)
async def get_financial_record(
    record_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get financial record by ID
    """
    record = financial_service.get(db, id=record_id)
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Financial record not found"
        )
    return record


@router.put("/{record_id}", response_model=FinancialRecordResponse)
async def update_financial_record(
    *,
    db: Session = Depends(get_db),
    record_id: int,
    record_in: FinancialRecordUpdate,
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Update financial record
    """
    record = financial_service.get(db, id=record_id)
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Financial record not found"
        )
    
    record = financial_service.update(db, db_obj=record, obj_in=record_in)
    return record


@router.post("/{record_id}/approve", response_model=FinancialRecordResponse)
async def approve_financial_record(
    *,
    db: Session = Depends(get_db),
    record_id: int,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Approve a financial record (requires superuser privileges)
    """
    try:
        record = financial_service.approve_record(
            db, record_id=record_id, approved_by=current_user
        )
        return record
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/stats/summary", response_model=FinancialStats)
async def get_financial_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get financial statistics summary
    """
    stats = financial_service.get_stats(db)
    return stats


@router.get("/dashboard/data", response_model=FinancialDashboard)
async def get_financial_dashboard(
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get financial dashboard data
    """
    dashboard_data = financial_service.get_dashboard_data(db)
    return dashboard_data


@router.get("/categories/income")
async def get_income_categories(
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get available income categories
    """
    return {
        "income_categories": [
            {"value": IncomeCategory.COURSE_FEES, "label": "Course Fees"},
            {"value": IncomeCategory.REGISTRATION_FEES, "label": "Registration Fees"},
            {"value": IncomeCategory.CONSULTATION_FEES, "label": "Consultation Fees"},
            {"value": IncomeCategory.WORKSHOP_FEES, "label": "Workshop Fees"},
            {"value": IncomeCategory.CERTIFICATION_FEES, "label": "Certification Fees"},
            {"value": IncomeCategory.OTHER_INCOME, "label": "Other Income"},
        ]
    }


@router.get("/categories/expense")
async def get_expense_categories(
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get available expense categories
    """
    return {
        "expense_categories": [
            {"value": ExpenseCategory.SALARIES, "label": "Salaries"},
            {"value": ExpenseCategory.RENT, "label": "Rent"},
            {"value": ExpenseCategory.UTILITIES, "label": "Utilities"},
            {"value": ExpenseCategory.EQUIPMENT, "label": "Equipment"},
            {"value": ExpenseCategory.SUPPLIES, "label": "Supplies"},
            {"value": ExpenseCategory.MARKETING, "label": "Marketing"},
            {"value": ExpenseCategory.INSURANCE, "label": "Insurance"},
            {"value": ExpenseCategory.MAINTENANCE, "label": "Maintenance"},
            {"value": ExpenseCategory.TRAVEL, "label": "Travel"},
            {"value": ExpenseCategory.TRAINING, "label": "Training"},
            {"value": ExpenseCategory.SOFTWARE_LICENSES, "label": "Software Licenses"},
            {"value": ExpenseCategory.OTHER_EXPENSES, "label": "Other Expenses"},
        ]
    }


@router.get("/statuses/list")
async def get_financial_statuses(
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get available financial record statuses
    """
    return {
        "financial_statuses": [
            {"value": FinancialRecordStatus.PENDING, "label": "Pending"},
            {"value": FinancialRecordStatus.APPROVED, "label": "Approved"},
            {"value": FinancialRecordStatus.REJECTED, "label": "Rejected"},
            {"value": FinancialRecordStatus.CANCELLED, "label": "Cancelled"},
        ]
    }
