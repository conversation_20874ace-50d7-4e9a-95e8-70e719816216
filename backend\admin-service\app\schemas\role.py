"""
Role schemas for API serialization
"""

from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, validator


# Shared properties
class RoleBase(BaseModel):
    name: str
    display_name: str
    description: Optional[str] = None
    permissions: List[str] = []
    is_active: bool = True

    @validator("name")
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Role name cannot be empty")
        # Convert to lowercase and replace spaces with underscores
        return v.lower().replace(" ", "_")

    @validator("display_name")
    def validate_display_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError("Display name cannot be empty")
        return v.strip()


# Properties to receive via API on creation
class RoleCreate(RoleBase):
    pass


# Properties to receive via API on update
class RoleUpdate(BaseModel):
    display_name: Optional[str] = None
    description: Optional[str] = None
    permissions: Optional[List[str]] = None
    is_active: Optional[bool] = None

    @validator("display_name")
    def validate_display_name(cls, v):
        if v is not None and len(v.strip()) == 0:
            raise ValueError("Display name cannot be empty")
        return v.strip() if v else v


class RoleInDBBase(RoleBase):
    id: Optional[int] = None
    is_system: bool = False
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True


# Additional properties to return via API
class RoleResponse(RoleInDBBase):
    user_count: Optional[int] = 0


# Additional properties stored in DB
class RoleInDB(RoleInDBBase):
    pass


# Role assignment
class RoleAssignment(BaseModel):
    user_id: int
    role_id: int


# Role with users
class RoleWithUsers(RoleResponse):
    users: List[dict] = []


# Permission info
class PermissionInfo(BaseModel):
    value: str
    label: str
    description: Optional[str] = None
    category: Optional[str] = None
