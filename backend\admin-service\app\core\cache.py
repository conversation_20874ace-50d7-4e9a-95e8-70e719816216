"""
Caching utilities and Redis configuration
"""

import json
import pickle
from typing import Any, Optional, Union
from datetime import timedelta
import redis.asyncio as redis
from app.core.config import settings


class CacheManager:
    """Redis cache manager for application caching."""
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
    
    async def connect(self):
        """Connect to Redis."""
        if not self.redis_client:
            self.redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )
    
    async def disconnect(self):
        """Disconnect from Redis."""
        if self.redis_client:
            await self.redis_client.close()
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if not self.redis_client:
            await self.connect()
        
        try:
            value = await self.redis_client.get(key)
            if value:
                return json.loads(value)
            return None
        except (json.JSONDecodeError, redis.RedisError):
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[Union[int, timedelta]] = None
    ) -> bool:
        """Set value in cache."""
        if not self.redis_client:
            await self.connect()
        
        try:
            serialized_value = json.dumps(value, default=str)
            if expire:
                if isinstance(expire, timedelta):
                    expire = int(expire.total_seconds())
                return await self.redis_client.setex(key, expire, serialized_value)
            else:
                return await self.redis_client.set(key, serialized_value)
        except (json.JSONEncodeError, redis.RedisError):
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache."""
        if not self.redis_client:
            await self.connect()
        
        try:
            return bool(await self.redis_client.delete(key))
        except redis.RedisError:
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        if not self.redis_client:
            await self.connect()
        
        try:
            return bool(await self.redis_client.exists(key))
        except redis.RedisError:
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern."""
        if not self.redis_client:
            await self.connect()
        
        try:
            keys = await self.redis_client.keys(pattern)
            if keys:
                return await self.redis_client.delete(*keys)
            return 0
        except redis.RedisError:
            return 0
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """Increment counter in cache."""
        if not self.redis_client:
            await self.connect()
        
        try:
            return await self.redis_client.incrby(key, amount)
        except redis.RedisError:
            return None
    
    async def set_hash(self, key: str, mapping: dict, expire: Optional[int] = None) -> bool:
        """Set hash in cache."""
        if not self.redis_client:
            await self.connect()
        
        try:
            result = await self.redis_client.hset(key, mapping=mapping)
            if expire:
                await self.redis_client.expire(key, expire)
            return bool(result)
        except redis.RedisError:
            return False
    
    async def get_hash(self, key: str) -> Optional[dict]:
        """Get hash from cache."""
        if not self.redis_client:
            await self.connect()
        
        try:
            return await self.redis_client.hgetall(key)
        except redis.RedisError:
            return None


# Global cache manager instance
cache_manager = CacheManager()


# Cache decorators
def cache_result(expire: int = 300, key_prefix: str = ""):
    """Decorator to cache function results."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # Try to get from cache
            cached_result = await cache_manager.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache_manager.set(cache_key, result, expire)
            return result
        
        return wrapper
    return decorator


# Cache key generators
class CacheKeys:
    """Cache key generators for different data types."""
    
    @staticmethod
    def user_profile(user_id: int) -> str:
        return f"user:profile:{user_id}"
    
    @staticmethod
    def user_permissions(user_id: int) -> str:
        return f"user:permissions:{user_id}"
    
    @staticmethod
    def payment_stats() -> str:
        return "stats:payments"
    
    @staticmethod
    def financial_stats() -> str:
        return "stats:financial"
    
    @staticmethod
    def dashboard_data() -> str:
        return "dashboard:data"
    
    @staticmethod
    def audit_stats() -> str:
        return "stats:audit"
    
    @staticmethod
    def system_config() -> str:
        return "system:config"
    
    @staticmethod
    def role_permissions(role_id: int) -> str:
        return f"role:permissions:{role_id}"


# Cache invalidation helpers
class CacheInvalidator:
    """Helper class for cache invalidation."""
    
    @staticmethod
    async def invalidate_user_cache(user_id: int):
        """Invalidate all user-related cache."""
        await cache_manager.delete(CacheKeys.user_profile(user_id))
        await cache_manager.delete(CacheKeys.user_permissions(user_id))
    
    @staticmethod
    async def invalidate_stats_cache():
        """Invalidate all statistics cache."""
        await cache_manager.delete(CacheKeys.payment_stats())
        await cache_manager.delete(CacheKeys.financial_stats())
        await cache_manager.delete(CacheKeys.audit_stats())
        await cache_manager.delete(CacheKeys.dashboard_data())
    
    @staticmethod
    async def invalidate_system_cache():
        """Invalidate system configuration cache."""
        await cache_manager.delete(CacheKeys.system_config())
    
    @staticmethod
    async def invalidate_role_cache(role_id: int):
        """Invalidate role-related cache."""
        await cache_manager.delete(CacheKeys.role_permissions(role_id))
        # Also invalidate user permissions for users with this role
        await cache_manager.clear_pattern("user:permissions:*")
