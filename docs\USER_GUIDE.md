# 👤 Innovative Centre Platform - User Guide

## 🚀 Getting Started

### First Login

1. **Access the Admin Portal**: Navigate to `http://localhost:3004`
2. **Default Credentials**:
   - **Email**: `<EMAIL>`
   - **Password**: `changeme`
3. **⚠️ Important**: Change the default password immediately after first login

### Dashboard Overview

The main dashboard provides:
- **Financial Summary**: Total income, expenses, and profit
- **Payment Statistics**: Recent payments and processing status
- **User Activity**: Recent user activities and system usage
- **Quick Actions**: Common tasks and shortcuts

## 👥 User Management

### Managing Users

1. **Navigate to Users**: Click "Users" in the sidebar
2. **View Users**: See all system users with their roles and status
3. **Create New User**:
   - Click "Add User" button
   - Fill in required information:
     - Email (must be unique)
     - Username (must be unique)
     - Full Name
     - Password (minimum 8 characters)
   - Assign initial role
   - Click "Create User"

### User Roles

**Available Roles:**
- **Super Admin**: Complete system access
- **Admin**: Administrative access to most features
- **Manager**: General management operations
- **Academic Manager**: Academic operations and course management
- **Financial Manager**: Financial and payment management
- **Accountant**: Financial management and accounting
- **Cashier**: Payment processing and cash handling
- **Reception**: Reception desk operations
- **Teacher**: Teaching and student management
- **Staff**: Basic staff access

### Assigning Roles

1. **Select User**: Click on user from the list
2. **Edit User**: Click "Edit" button
3. **Assign Role**: Select role from dropdown
4. **Save Changes**: Click "Update User"

## 💳 Payment Management

### Recording Payments

1. **Navigate to Payments**: Click "Payments" in sidebar
2. **Add New Payment**:
   - Click "Record Payment" button
   - Enter student information:
     - Student Name
     - Student Email
     - Course Name (optional)
   - Payment details:
     - Amount
     - Payment Method (Card, Cash, Bank Transfer, etc.)
     - Description
   - Click "Record Payment"

### Payment Methods

- **Card**: Credit/Debit card payments
- **Cash**: Cash payments
- **Bank Transfer**: Direct bank transfers
- **Check**: Check payments
- **Online**: Online payment platforms
- **Installment**: Installment payments

### Processing Payments

1. **Find Payment**: Use search or filters to find payment
2. **Process Payment**: Click "Process" button
3. **Confirm**: Verify payment details and confirm processing
4. **Status Update**: Payment status changes to "Completed"

### Refunding Payments

1. **Select Payment**: Find completed payment
2. **Initiate Refund**: Click "Refund" button
3. **Refund Details**:
   - Refund Amount (can be partial)
   - Refund Reason
4. **Process Refund**: Click "Process Refund"

## 💰 Financial Management

### Recording Income

1. **Navigate to Financial**: Click "Financial" in sidebar
2. **Add Income Record**:
   - Click "Add Record" button
   - Select Type: "Income"
   - Choose Category:
     - Course Fees
     - Consulting
     - Materials
     - Other Income
   - Enter amount and description
   - Add reference number (optional)
   - Click "Save Record"

### Recording Expenses

1. **Add Expense Record**:
   - Select Type: "Expense"
   - Choose Category:
     - Salaries
     - Utilities
     - Rent
     - Office Supplies
     - Marketing
     - Other Expenses
   - Enter details and save

### Approval Workflow

1. **Pending Records**: New records start as "Pending"
2. **Review**: Financial managers review records
3. **Approve/Reject**: Click "Approve" or "Reject" with reason
4. **Status Update**: Record status updates accordingly

### Financial Reports

- **Dashboard**: Overview of financial health
- **Monthly Trends**: Income and expense trends
- **Category Analysis**: Breakdown by categories
- **Profit/Loss**: Net profit calculations

## ⚙️ System Configuration

### General Settings

1. **Navigate to Settings**: Click "Settings" in sidebar
2. **General Tab**: Basic system settings
   - Organization Name
   - Contact Information
   - Time Zone
   - Language

### Email Configuration

1. **Email Tab**: Email server settings
   - SMTP Host
   - SMTP Port
   - Username/Password
   - Security Settings (TLS/SSL)

### Payment Settings

1. **Payment Tab**: Payment processing settings
   - Accepted Payment Methods
   - Currency Settings
   - Tax Configuration
   - Payment Terms

### Security Settings

1. **Security Tab**: Security configurations
   - Password Policy
   - Session Timeout
   - Two-Factor Authentication
   - Login Attempt Limits

## 📋 Audit & Monitoring

### Viewing Audit Logs

1. **Navigate to Audit**: Click "Audit" in sidebar
2. **Filter Logs**: Use filters to find specific activities:
   - Date Range
   - User
   - Action Type
   - Resource Type

### Security Monitoring

1. **Security Report**: View security analysis
   - Failed Login Attempts
   - Suspicious Activities
   - IP Address Tracking
   - User Activity Patterns

### Exporting Data

1. **Export Audit Logs**:
   - Select date range
   - Choose format (CSV, JSON)
   - Apply filters
   - Click "Export"

2. **Export Financial Data**:
   - Navigate to Financial section
   - Click "Export" button
   - Select format and date range

## 🔍 Search & Filters

### Global Search

- **Search Bar**: Located in top navigation
- **Search Types**: Users, payments, financial records
- **Quick Results**: Instant search results as you type

### Advanced Filters

**Payment Filters:**
- Status (Pending, Completed, Refunded)
- Payment Method
- Date Range
- Amount Range
- Student Name

**Financial Filters:**
- Type (Income, Expense)
- Category
- Status (Pending, Approved, Rejected)
- Date Range
- Amount Range

**User Filters:**
- Role
- Status (Active, Inactive)
- Registration Date
- Last Login

## 📱 Mobile Usage

### Responsive Design

- **Mobile Friendly**: Interface adapts to mobile screens
- **Touch Optimized**: Buttons and forms optimized for touch
- **Essential Features**: Core functionality available on mobile

### Mobile Best Practices

- **Portrait Mode**: Best experience in portrait orientation
- **WiFi Recommended**: For better performance
- **Regular Sync**: Ensure data synchronization

## 🆘 Troubleshooting

### Common Issues

**Login Problems:**
- Check email/password spelling
- Ensure account is active
- Contact admin if locked out

**Payment Issues:**
- Verify payment details
- Check user permissions
- Ensure payment method is enabled

**Performance Issues:**
- Clear browser cache
- Check internet connection
- Try different browser

### Getting Help

1. **Documentation**: Check this user guide
2. **System Admin**: Contact your system administrator
3. **Support**: Use built-in help system
4. **Training**: Request additional training if needed

## 🔐 Security Best Practices

### Password Security

- **Strong Passwords**: Use complex passwords
- **Regular Updates**: Change passwords regularly
- **Unique Passwords**: Don't reuse passwords
- **Two-Factor**: Enable 2FA when available

### Account Security

- **Logout**: Always logout when finished
- **Shared Computers**: Don't save passwords on shared computers
- **Suspicious Activity**: Report unusual activity immediately
- **Regular Review**: Review your account activity regularly

### Data Protection

- **Sensitive Data**: Handle student/financial data carefully
- **Access Control**: Only access data you need
- **Sharing**: Don't share login credentials
- **Backup**: Ensure important data is backed up

## 📞 Support & Contact

### Technical Support

- **System Issues**: Contact IT department
- **User Training**: Request training sessions
- **Feature Requests**: Submit through proper channels

### Emergency Contacts

- **System Down**: Contact emergency IT support
- **Security Incident**: Report immediately to security team
- **Data Loss**: Contact backup/recovery team immediately

---

**Remember**: This system handles sensitive educational and financial data. Always follow your organization's data protection policies and procedures.
