"""
User management tests
"""

import pytest
from fastapi.testclient import Test<PERSON><PERSON>

from app.core.config import settings


class TestUserManagement:
    """Test user management endpoints."""

    def test_get_users_list(self, client: TestClient, admin_auth_headers, test_user):
        """Test getting list of users (admin only)."""
        response = client.get(
            f"{settings.API_V1_STR}/users/",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) >= 1

    def test_get_users_list_unauthorized(self, client: TestClient, auth_headers):
        """Test getting users list without admin privileges."""
        response = client.get(
            f"{settings.API_V1_STR}/users/",
            headers=auth_headers
        )
        # Should return 403 if user doesn't have admin privileges
        assert response.status_code in [403, 200]  # Depends on implementation

    def test_create_user(self, client: TestClient, admin_auth_headers):
        """Test creating a new user."""
        user_data = {
            "email": "<EMAIL>",
            "username": "newuser",
            "full_name": "New User",
            "password": "newpassword123"
        }
        response = client.post(
            f"{settings.API_V1_STR}/users/",
            json=user_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 201
        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["username"] == user_data["username"]
        assert "id" in data

    def test_create_user_duplicate_email(self, client: TestClient, admin_auth_headers, test_user):
        """Test creating user with duplicate email."""
        user_data = {
            "email": test_user.email,
            "username": "differentusername",
            "full_name": "Different User",
            "password": "password123"
        }
        response = client.post(
            f"{settings.API_V1_STR}/users/",
            json=user_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 400

    def test_get_user_by_id(self, client: TestClient, admin_auth_headers, test_user):
        """Test getting user by ID."""
        response = client.get(
            f"{settings.API_V1_STR}/users/{test_user.id}",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_user.id
        assert data["email"] == test_user.email

    def test_get_nonexistent_user(self, client: TestClient, admin_auth_headers):
        """Test getting nonexistent user."""
        response = client.get(
            f"{settings.API_V1_STR}/users/99999",
            headers=admin_auth_headers
        )
        assert response.status_code == 404

    def test_update_user(self, client: TestClient, admin_auth_headers, test_user):
        """Test updating user information."""
        update_data = {
            "full_name": "Updated Name",
            "is_active": True
        }
        response = client.patch(
            f"{settings.API_V1_STR}/users/{test_user.id}",
            json=update_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["full_name"] == update_data["full_name"]

    def test_delete_user(self, client: TestClient, admin_auth_headers, test_user):
        """Test deleting a user."""
        response = client.delete(
            f"{settings.API_V1_STR}/users/{test_user.id}",
            headers=admin_auth_headers
        )
        assert response.status_code == 200

        # Verify user is deleted
        get_response = client.get(
            f"{settings.API_V1_STR}/users/{test_user.id}",
            headers=admin_auth_headers
        )
        assert get_response.status_code == 404


class TestUserRoles:
    """Test user role assignment."""

    def test_assign_role_to_user(self, client: TestClient, admin_auth_headers, test_user, test_role):
        """Test assigning role to user."""
        response = client.post(
            f"{settings.API_V1_STR}/users/{test_user.id}/assign-role",
            json={"role_id": test_role.id},
            headers=admin_auth_headers
        )
        assert response.status_code == 200

    def test_assign_nonexistent_role(self, client: TestClient, admin_auth_headers, test_user):
        """Test assigning nonexistent role to user."""
        response = client.post(
            f"{settings.API_V1_STR}/users/{test_user.id}/assign-role",
            json={"role_id": 99999},
            headers=admin_auth_headers
        )
        assert response.status_code == 404

    def test_get_user_roles(self, client: TestClient, admin_auth_headers, test_user):
        """Test getting user's roles."""
        response = client.get(
            f"{settings.API_V1_STR}/users/{test_user.id}/roles",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
