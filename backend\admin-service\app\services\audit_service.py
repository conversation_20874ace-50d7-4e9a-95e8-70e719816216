"""
Audit service for comprehensive audit logging and reporting
"""

import csv
import json
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from io import String<PERSON>

from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc

from app.models.audit_log import AuditLog, AuditActions, AuditResourceTypes
from app.models.user import User
from app.schemas.audit_log import (
    AuditLogFilter, AuditStats, SecurityReport, ActivitySummary,
    UserActivityReport, AuditExportRequest
)


class AuditService:
    def get_filtered_logs(
        self, 
        db: Session, 
        *, 
        filters: AuditLogFilter,
        skip: int = 0,
        limit: int = 100
    ) -> List[AuditLog]:
        """Get audit logs with filters"""
        query = db.query(AuditLog).join(User, AuditLog.user_id == User.id, isouter=True)
        
        if filters.user_id:
            query = query.filter(AuditLog.user_id == filters.user_id)
        
        if filters.action:
            query = query.filter(AuditLog.action == filters.action)
        
        if filters.resource_type:
            query = query.filter(AuditLog.resource_type == filters.resource_type)
        
        if filters.resource_id:
            query = query.filter(AuditLog.resource_id == filters.resource_id)
        
        if filters.status:
            query = query.filter(AuditLog.status == filters.status)
        
        if filters.date_from:
            query = query.filter(AuditLog.created_at >= filters.date_from)
        
        if filters.date_to:
            query = query.filter(AuditLog.created_at <= filters.date_to)
        
        if filters.ip_address:
            query = query.filter(AuditLog.ip_address == filters.ip_address)
        
        if filters.search:
            search_term = f"%{filters.search}%"
            query = query.filter(
                or_(
                    AuditLog.description.ilike(search_term),
                    AuditLog.endpoint.ilike(search_term),
                    User.email.ilike(search_term)
                )
            )
        
        return query.order_by(desc(AuditLog.created_at)).offset(skip).limit(limit).all()

    def get_audit_stats(self, db: Session, *, filters: Optional[AuditLogFilter] = None) -> AuditStats:
        """Get comprehensive audit statistics"""
        query = db.query(AuditLog)
        
        # Apply filters if provided
        if filters:
            if filters.date_from:
                query = query.filter(AuditLog.created_at >= filters.date_from)
            if filters.date_to:
                query = query.filter(AuditLog.created_at <= filters.date_to)
            if filters.user_id:
                query = query.filter(AuditLog.user_id == filters.user_id)
        
        # Get basic stats
        total_logs = query.count()
        successful_actions = query.filter(AuditLog.status == "SUCCESS").count()
        failed_actions = query.filter(AuditLog.status != "SUCCESS").count()
        
        # Unique users and IPs
        unique_users = query.filter(AuditLog.user_id.isnot(None)).distinct(AuditLog.user_id).count()
        unique_ips = query.filter(AuditLog.ip_address.isnot(None)).distinct(AuditLog.ip_address).count()
        
        # Top actions
        top_actions_query = query.with_entities(
            AuditLog.action,
            func.count(AuditLog.id).label('count')
        ).group_by(AuditLog.action).order_by(desc('count')).limit(10)
        
        top_actions = [
            {"action": row.action, "count": row.count}
            for row in top_actions_query.all()
        ]
        
        # Top resources
        top_resources_query = query.with_entities(
            AuditLog.resource_type,
            func.count(AuditLog.id).label('count')
        ).group_by(AuditLog.resource_type).order_by(desc('count')).limit(10)
        
        top_resources = [
            {"resource_type": row.resource_type, "count": row.count}
            for row in top_resources_query.all()
        ]
        
        # Recent activity (last 10)
        recent_logs = query.order_by(desc(AuditLog.created_at)).limit(10).all()
        recent_activity = []
        for log in recent_logs:
            user = db.query(User).filter(User.id == log.user_id).first() if log.user_id else None
            recent_activity.append({
                "id": log.id,
                "action": log.action,
                "resource_type": log.resource_type,
                "user_email": user.email if user else None,
                "created_at": log.created_at,
                "status": log.status
            })
        
        return AuditStats(
            total_logs=total_logs,
            successful_actions=successful_actions,
            failed_actions=failed_actions,
            unique_users=unique_users,
            unique_ips=unique_ips,
            top_actions=top_actions,
            top_resources=top_resources,
            recent_activity=recent_activity
        )

    def get_security_report(self, db: Session, *, days: int = 30) -> SecurityReport:
        """Generate security report for the last N days"""
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Failed logins
        failed_logins = db.query(AuditLog).filter(
            AuditLog.action == AuditActions.LOGIN_FAILED,
            AuditLog.created_at >= start_date
        ).count()
        
        # Suspicious activities (multiple failed logins from same IP)
        suspicious_ips = db.query(
            AuditLog.ip_address,
            func.count(AuditLog.id).label('count')
        ).filter(
            AuditLog.action == AuditActions.LOGIN_FAILED,
            AuditLog.created_at >= start_date,
            AuditLog.ip_address.isnot(None)
        ).group_by(AuditLog.ip_address).having(func.count(AuditLog.id) >= 5).all()
        
        suspicious_activities = len(suspicious_ips)
        
        # Admin actions
        admin_actions = db.query(AuditLog).join(User).filter(
            User.is_superuser == True,
            AuditLog.created_at >= start_date
        ).count()
        
        # Permission changes
        permission_changes = db.query(AuditLog).filter(
            or_(
                AuditLog.action == AuditActions.PERMISSION_GRANT,
                AuditLog.action == AuditActions.PERMISSION_REVOKE
            ),
            AuditLog.created_at >= start_date
        ).count()
        
        # Config changes
        config_changes = db.query(AuditLog).filter(
            AuditLog.action == AuditActions.CONFIG_CHANGE,
            AuditLog.created_at >= start_date
        ).count()
        
        # Unusual access patterns
        unusual_patterns = []
        for ip, count in suspicious_ips:
            unusual_patterns.append({
                "type": "Multiple failed logins",
                "ip_address": ip,
                "count": count,
                "severity": "high" if count >= 10 else "medium"
            })
        
        # IP analysis
        ip_analysis = []
        top_ips = db.query(
            AuditLog.ip_address,
            func.count(AuditLog.id).label('total_requests'),
            func.count(func.distinct(AuditLog.user_id)).label('unique_users')
        ).filter(
            AuditLog.created_at >= start_date,
            AuditLog.ip_address.isnot(None)
        ).group_by(AuditLog.ip_address).order_by(desc('total_requests')).limit(10).all()
        
        for ip, total, unique_users in top_ips:
            ip_analysis.append({
                "ip_address": ip,
                "total_requests": total,
                "unique_users": unique_users,
                "risk_score": "high" if total > 1000 and unique_users == 1 else "low"
            })
        
        return SecurityReport(
            failed_logins=failed_logins,
            suspicious_activities=suspicious_activities,
            admin_actions=admin_actions,
            permission_changes=permission_changes,
            config_changes=config_changes,
            unusual_access_patterns=unusual_patterns,
            ip_analysis=ip_analysis
        )

    def get_activity_summary(self, db: Session, *, days: int = 30) -> List[ActivitySummary]:
        """Get daily activity summary for the last N days"""
        start_date = datetime.utcnow() - timedelta(days=days)
        
        daily_stats = db.query(
            func.date(AuditLog.created_at).label('date'),
            func.count(AuditLog.id).label('total_actions'),
            func.sum(func.case([(AuditLog.status == 'SUCCESS', 1)], else_=0)).label('successful'),
            func.sum(func.case([(AuditLog.status != 'SUCCESS', 1)], else_=0)).label('failed'),
            func.count(func.distinct(AuditLog.user_id)).label('unique_users')
        ).filter(
            AuditLog.created_at >= start_date
        ).group_by(func.date(AuditLog.created_at)).order_by('date').all()
        
        summary = []
        for stat in daily_stats:
            # Get top action for the day
            top_action = db.query(
                AuditLog.action,
                func.count(AuditLog.id).label('count')
            ).filter(
                func.date(AuditLog.created_at) == stat.date
            ).group_by(AuditLog.action).order_by(desc('count')).first()
            
            summary.append(ActivitySummary(
                date=stat.date.isoformat(),
                total_actions=stat.total_actions,
                successful_actions=stat.successful or 0,
                failed_actions=stat.failed or 0,
                unique_users=stat.unique_users,
                top_action=top_action.action if top_action else None
            ))
        
        return summary

    def get_user_activity_report(self, db: Session, *, user_id: int) -> UserActivityReport:
        """Get detailed activity report for a specific user"""
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise ValueError(f"User {user_id} not found")
        
        # Basic stats
        total_actions = db.query(AuditLog).filter(AuditLog.user_id == user_id).count()
        successful_actions = db.query(AuditLog).filter(
            AuditLog.user_id == user_id,
            AuditLog.status == "SUCCESS"
        ).count()
        failed_actions = total_actions - successful_actions
        
        # Last activity
        last_log = db.query(AuditLog).filter(
            AuditLog.user_id == user_id
        ).order_by(desc(AuditLog.created_at)).first()
        
        # Top actions
        top_actions_query = db.query(
            AuditLog.action,
            func.count(AuditLog.id).label('count')
        ).filter(
            AuditLog.user_id == user_id
        ).group_by(AuditLog.action).order_by(desc('count')).limit(5)
        
        top_actions = [
            {"action": row.action, "count": row.count}
            for row in top_actions_query.all()
        ]
        
        # Recent activities
        recent_logs = db.query(AuditLog).filter(
            AuditLog.user_id == user_id
        ).order_by(desc(AuditLog.created_at)).limit(20).all()
        
        return UserActivityReport(
            user_id=user_id,
            user_email=user.email,
            user_name=user.full_name,
            total_actions=total_actions,
            successful_actions=successful_actions,
            failed_actions=failed_actions,
            last_activity=last_log.created_at if last_log else None,
            top_actions=top_actions,
            recent_activities=recent_logs
        )

    def export_audit_logs(
        self, 
        db: Session, 
        *, 
        export_request: AuditExportRequest
    ) -> str:
        """Export audit logs in specified format"""
        logs = self.get_filtered_logs(db, filters=export_request.filters, limit=10000)
        
        if export_request.format.lower() == "csv":
            return self._export_to_csv(logs, export_request.include_sensitive)
        elif export_request.format.lower() == "json":
            return self._export_to_json(logs, export_request.include_sensitive)
        else:
            raise ValueError(f"Unsupported export format: {export_request.format}")

    def _export_to_csv(self, logs: List[AuditLog], include_sensitive: bool) -> str:
        """Export logs to CSV format"""
        output = StringIO()
        fieldnames = [
            'id', 'created_at', 'user_id', 'action', 'resource_type', 
            'resource_id', 'status', 'ip_address', 'endpoint', 'method', 'description'
        ]
        
        if include_sensitive:
            fieldnames.extend(['old_values', 'new_values', 'metadata'])
        
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        
        for log in logs:
            row = {
                'id': log.id,
                'created_at': log.created_at.isoformat() if log.created_at else None,
                'user_id': log.user_id,
                'action': log.action,
                'resource_type': log.resource_type,
                'resource_id': log.resource_id,
                'status': log.status,
                'ip_address': log.ip_address,
                'endpoint': log.endpoint,
                'method': log.method,
                'description': log.description
            }
            
            if include_sensitive:
                row.update({
                    'old_values': json.dumps(log.old_values) if log.old_values else None,
                    'new_values': json.dumps(log.new_values) if log.new_values else None,
                    'metadata': json.dumps(log.metadata) if log.metadata else None
                })
            
            writer.writerow(row)
        
        return output.getvalue()

    def _export_to_json(self, logs: List[AuditLog], include_sensitive: bool) -> str:
        """Export logs to JSON format"""
        data = []
        for log in logs:
            item = {
                'id': log.id,
                'created_at': log.created_at.isoformat() if log.created_at else None,
                'user_id': log.user_id,
                'action': log.action,
                'resource_type': log.resource_type,
                'resource_id': log.resource_id,
                'status': log.status,
                'ip_address': log.ip_address,
                'endpoint': log.endpoint,
                'method': log.method,
                'description': log.description
            }
            
            if include_sensitive:
                item.update({
                    'old_values': log.old_values,
                    'new_values': log.new_values,
                    'metadata': log.metadata
                })
            
            data.append(item)
        
        return json.dumps(data, indent=2, default=str)


# Create audit service instance
audit_service = AuditService()
