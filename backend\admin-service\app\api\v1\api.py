"""
API v1 router configuration
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, health, payments, financial, roles, system_config, audit

# Create API router
api_router = APIRouter()

# Include endpoint routers
api_router.include_router(health.router, prefix="/health", tags=["health"])
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(roles.router, prefix="/roles", tags=["roles"])
api_router.include_router(payments.router, prefix="/payments", tags=["payments"])
api_router.include_router(financial.router, prefix="/financial", tags=["financial"])
api_router.include_router(system_config.router, prefix="/system-config", tags=["system-config"])
api_router.include_router(audit.router, prefix="/audit", tags=["audit"])
