"""
Role management endpoints
"""

from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.user import User
from app.models.role import Role, Permissions
from app.schemas.role import RoleCreate, RoleResponse, RoleUpdate
from app.services.user_service import user_service
from app.services.role_service import role_service

router = APIRouter()


@router.get("/", response_model=List[RoleResponse])
async def get_roles(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Retrieve roles
    """
    roles = role_service.get_multi(db, skip=skip, limit=limit)
    return roles


@router.post("/", response_model=RoleResponse)
async def create_role(
    *,
    db: Session = Depends(get_db),
    role_in: RoleCreate,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Create new role (superuser only)
    """
    role = role_service.get_by_name(db, name=role_in.name)
    if role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Role with this name already exists"
        )
    
    role = role_service.create(db, obj_in=role_in, created_by=current_user)
    return role


@router.get("/{role_id}", response_model=RoleResponse)
async def get_role(
    role_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get role by ID
    """
    role = role_service.get(db, id=role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    return role


@router.put("/{role_id}", response_model=RoleResponse)
async def update_role(
    *,
    db: Session = Depends(get_db),
    role_id: int,
    role_in: RoleUpdate,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Update role (superuser only)
    """
    role = role_service.get(db, id=role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    if role.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot modify system roles"
        )
    
    role = role_service.update(db, db_obj=role, obj_in=role_in)
    return role


@router.delete("/{role_id}")
async def delete_role(
    *,
    db: Session = Depends(get_db),
    role_id: int,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Delete role (superuser only)
    """
    role = role_service.get(db, id=role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    if role.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete system roles"
        )
    
    # Check if role is assigned to any users
    users_with_role = role_service.get_users_with_role(db, role_id=role_id)
    if users_with_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Cannot delete role. {len(users_with_role)} users are assigned to this role."
        )
    
    role_service.remove(db, id=role_id)
    return {"message": "Role deleted successfully"}


@router.get("/permissions/list")
async def get_available_permissions(
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Get all available permissions
    """
    permissions = Permissions.get_all_permissions()
    return {
        "permissions": [
            {"value": perm, "label": perm.replace(":", " ").replace("_", " ").title()}
            for perm in permissions
        ]
    }


@router.post("/{role_id}/permissions/{permission}")
async def add_permission_to_role(
    *,
    db: Session = Depends(get_db),
    role_id: int,
    permission: str,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Add permission to role
    """
    role = role_service.get(db, id=role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    if role.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot modify system roles"
        )
    
    available_permissions = Permissions.get_all_permissions()
    if permission not in available_permissions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid permission"
        )
    
    role_service.add_permission(db, role=role, permission=permission)
    return {"message": f"Permission {permission} added to role {role.name}"}


@router.delete("/{role_id}/permissions/{permission}")
async def remove_permission_from_role(
    *,
    db: Session = Depends(get_db),
    role_id: int,
    permission: str,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Remove permission from role
    """
    role = role_service.get(db, id=role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )
    
    if role.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot modify system roles"
        )
    
    role_service.remove_permission(db, role=role, permission=permission)
    return {"message": f"Permission {permission} removed from role {role.name}"}
