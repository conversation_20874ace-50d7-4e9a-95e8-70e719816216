"""
Payment service for payment management
"""

import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from decimal import Decimal

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from app.models.payment import Payment, PaymentStatus, PaymentMethod
from app.models.user import User
from app.schemas.payment import PaymentCreate, PaymentUpdate, PaymentFilter, PaymentStats
from app.services.base_service import BaseService
from app.utils.audit import AuditLogger, AuditActions, AuditResourceTypes


class PaymentService(BaseService[Payment, PaymentCreate, PaymentUpdate]):
    def create(self, db: Session, *, obj_in: PaymentCreate, created_by: User) -> Payment:
        """Create a new payment record"""
        # Generate payment ID if not provided
        payment_id = obj_in.payment_id or f"PAY-{uuid.uuid4().hex[:8].upper()}"
        
        # Create payment object
        obj_in_data = obj_in.dict(exclude={"payment_id"})
        db_obj = Payment(
            payment_id=payment_id,
            **obj_in_data
        )
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        # Log audit trail
        AuditLogger.log_create(
            db=db,
            user=created_by,
            resource_type=AuditResourceTypes.PAYMENT,
            resource_id=str(db_obj.id),
            new_values=obj_in.dict(),
            description=f"Created payment {payment_id}"
        )
        
        return db_obj

    def process_payment(
        self, 
        db: Session, 
        *, 
        payment_id: str, 
        transaction_id: Optional[str] = None,
        gateway_response: Optional[Dict[str, Any]] = None,
        processed_by: User
    ) -> Payment:
        """Process a payment (mark as completed)"""
        payment = self.get_by_payment_id(db, payment_id=payment_id)
        if not payment:
            raise ValueError(f"Payment {payment_id} not found")
        
        if payment.status != PaymentStatus.PENDING:
            raise ValueError(f"Payment {payment_id} is not in pending status")
        
        old_values = {
            "status": payment.status,
            "transaction_id": payment.transaction_id,
            "processed_at": payment.processed_at,
            "processed_by_id": payment.processed_by_id
        }
        
        # Update payment
        payment.status = PaymentStatus.COMPLETED
        payment.transaction_id = transaction_id
        payment.gateway_response = gateway_response
        payment.processed_at = datetime.utcnow()
        payment.processed_by_id = processed_by.id
        
        db.add(payment)
        db.commit()
        db.refresh(payment)
        
        # Log audit trail
        AuditLogger.log_action(
            db=db,
            user=processed_by,
            action=AuditActions.PAYMENT_PROCESS,
            resource_type=AuditResourceTypes.PAYMENT,
            resource_id=str(payment.id),
            old_values=old_values,
            new_values={
                "status": payment.status,
                "transaction_id": payment.transaction_id,
                "processed_at": payment.processed_at.isoformat() if payment.processed_at else None,
                "processed_by_id": payment.processed_by_id
            },
            description=f"Processed payment {payment_id}"
        )
        
        return payment

    def refund_payment(
        self,
        db: Session,
        *,
        payment_id: str,
        refund_amount: Decimal,
        refund_reason: str,
        processed_by: User
    ) -> Payment:
        """Refund a payment (partial or full)"""
        payment = self.get_by_payment_id(db, payment_id=payment_id)
        if not payment:
            raise ValueError(f"Payment {payment_id} not found")
        
        if payment.status != PaymentStatus.COMPLETED:
            raise ValueError(f"Payment {payment_id} is not completed")
        
        current_refunded = payment.refunded_amount or Decimal('0')
        total_refunded = current_refunded + refund_amount
        
        if total_refunded > payment.amount:
            raise ValueError("Refund amount exceeds payment amount")
        
        old_values = {
            "refunded_amount": payment.refunded_amount,
            "refund_reason": payment.refund_reason,
            "status": payment.status
        }
        
        # Update payment
        payment.refunded_amount = total_refunded
        payment.refund_reason = refund_reason
        
        # Update status based on refund amount
        if total_refunded >= payment.amount:
            payment.status = PaymentStatus.REFUNDED
        else:
            payment.status = PaymentStatus.PARTIALLY_REFUNDED
        
        db.add(payment)
        db.commit()
        db.refresh(payment)
        
        # Log audit trail
        AuditLogger.log_action(
            db=db,
            user=processed_by,
            action=AuditActions.PAYMENT_REFUND,
            resource_type=AuditResourceTypes.PAYMENT,
            resource_id=str(payment.id),
            old_values=old_values,
            new_values={
                "refunded_amount": float(payment.refunded_amount),
                "refund_reason": payment.refund_reason,
                "status": payment.status
            },
            description=f"Refunded {refund_amount} for payment {payment_id}"
        )
        
        return payment

    def get_by_payment_id(self, db: Session, *, payment_id: str) -> Optional[Payment]:
        """Get payment by payment ID"""
        return db.query(Payment).filter(Payment.payment_id == payment_id).first()

    def get_filtered(
        self, 
        db: Session, 
        *, 
        filters: PaymentFilter,
        skip: int = 0,
        limit: int = 100
    ) -> List[Payment]:
        """Get payments with filters"""
        query = db.query(Payment)
        
        if filters.status:
            query = query.filter(Payment.status == filters.status)
        
        if filters.payment_method:
            query = query.filter(Payment.payment_method == filters.payment_method)
        
        if filters.user_id:
            query = query.filter(Payment.user_id == filters.user_id)
        
        if filters.date_from:
            query = query.filter(Payment.created_at >= filters.date_from)
        
        if filters.date_to:
            query = query.filter(Payment.created_at <= filters.date_to)
        
        if filters.amount_min:
            query = query.filter(Payment.amount >= filters.amount_min)
        
        if filters.amount_max:
            query = query.filter(Payment.amount <= filters.amount_max)
        
        if filters.purpose:
            query = query.filter(Payment.purpose.ilike(f"%{filters.purpose}%"))
        
        return query.order_by(Payment.created_at.desc()).offset(skip).limit(limit).all()

    def get_stats(self, db: Session, *, filters: Optional[PaymentFilter] = None) -> PaymentStats:
        """Get payment statistics"""
        query = db.query(Payment)
        
        # Apply filters if provided
        if filters:
            if filters.date_from:
                query = query.filter(Payment.created_at >= filters.date_from)
            if filters.date_to:
                query = query.filter(Payment.created_at <= filters.date_to)
            if filters.user_id:
                query = query.filter(Payment.user_id == filters.user_id)
        
        # Get all payments for stats
        payments = query.all()
        
        total_payments = len(payments)
        total_amount = sum(p.amount for p in payments)
        
        completed_payments = [p for p in payments if p.status == PaymentStatus.COMPLETED]
        completed_count = len(completed_payments)
        completed_amount = sum(p.amount for p in completed_payments)
        
        pending_payments = [p for p in payments if p.status == PaymentStatus.PENDING]
        pending_count = len(pending_payments)
        pending_amount = sum(p.amount for p in pending_payments)
        
        failed_payments = [p for p in payments if p.status == PaymentStatus.FAILED]
        failed_count = len(failed_payments)
        
        refunded_amount = sum(p.refunded_amount or Decimal('0') for p in payments)
        
        return PaymentStats(
            total_payments=total_payments,
            total_amount=total_amount,
            completed_payments=completed_count,
            completed_amount=completed_amount,
            pending_payments=pending_count,
            pending_amount=pending_amount,
            failed_payments=failed_count,
            refunded_amount=refunded_amount
        )


# Create payment service instance
payment_service = PaymentService(Payment)
