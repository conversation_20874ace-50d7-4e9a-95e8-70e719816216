#!/usr/bin/env python3
"""
Project validation script for Innovative Centre Platform
This script validates the project structure and key files
"""

import os
import json
import sys
from pathlib import Path

def check_file_exists(file_path, description):
    """Check if a file exists and report status"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - NOT FOUND")
        return False

def check_directory_exists(dir_path, description):
    """Check if a directory exists and report status"""
    if os.path.isdir(dir_path):
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} - NOT FOUND")
        return False

def validate_json_file(file_path, description):
    """Validate JSON file syntax"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            json.load(f)
        print(f"✅ {description}: Valid JSON")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ {description}: Invalid JSON - {e}")
        return False
    except FileNotFoundError:
        print(f"❌ {description}: File not found")
        return False

def validate_python_syntax(file_path, description):
    """Basic Python syntax validation"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Basic syntax check
        compile(content, file_path, 'exec')
        print(f"✅ {description}: Valid Python syntax")
        return True
    except SyntaxError as e:
        print(f"❌ {description}: Syntax error - {e}")
        return False
    except FileNotFoundError:
        print(f"❌ {description}: File not found")
        return False

def main():
    """Main validation function"""
    print("🔍 Validating Innovative Centre Platform Project Structure")
    print("=" * 60)
    
    # Get project root
    project_root = Path(__file__).parent
    os.chdir(project_root)
    
    passed = 0
    total = 0
    
    # Core project structure
    print("\n📁 PROJECT STRUCTURE")
    print("-" * 30)
    
    structure_checks = [
        ("backend", "Backend directory"),
        ("frontend", "Frontend directory"),
        ("docs", "Documentation directory"),
        ("scripts", "Scripts directory"),
        ("backend/admin-service", "Admin service directory"),
        ("frontend/admin-portal", "Admin portal directory"),
    ]
    
    for path, desc in structure_checks:
        total += 1
        if check_directory_exists(path, desc):
            passed += 1
    
    # Backend files
    print("\n🐍 BACKEND FILES")
    print("-" * 30)
    
    backend_files = [
        ("backend/admin-service/main.py", "Main FastAPI application"),
        ("backend/admin-service/requirements.txt", "Python dependencies"),
        ("backend/admin-service/Dockerfile", "Docker configuration"),
        ("backend/admin-service/Dockerfile.prod", "Production Docker config"),
        ("backend/admin-service/app/__init__.py", "App package"),
        ("backend/admin-service/app/api/v1/api.py", "API router"),
        ("backend/admin-service/app/core/config.py", "Configuration"),
        ("backend/admin-service/app/models/__init__.py", "Models package"),
        ("backend/admin-service/tests/conftest.py", "Test configuration"),
        ("backend/admin-service/run_tests.py", "Test runner"),
    ]
    
    for path, desc in backend_files:
        total += 1
        if check_file_exists(path, desc):
            passed += 1
    
    # Validate key Python files
    print("\n🔍 PYTHON SYNTAX VALIDATION")
    print("-" * 30)
    
    python_files = [
        ("backend/admin-service/main.py", "Main application"),
        ("validate_project.py", "This validation script"),
    ]
    
    for path, desc in python_files:
        total += 1
        if validate_python_syntax(path, desc):
            passed += 1
    
    # Frontend files
    print("\n⚛️ FRONTEND FILES")
    print("-" * 30)
    
    frontend_files = [
        ("frontend/admin-portal/package.json", "Package configuration"),
        ("frontend/admin-portal/next.config.js", "Next.js configuration"),
        ("frontend/admin-portal/tailwind.config.js", "Tailwind configuration"),
        ("frontend/admin-portal/tsconfig.json", "TypeScript configuration"),
        ("frontend/admin-portal/Dockerfile.prod", "Production Docker config"),
        ("frontend/admin-portal/src/app/layout.tsx", "App layout"),
        ("frontend/admin-portal/src/app/page.tsx", "Home page"),
    ]
    
    for path, desc in frontend_files:
        total += 1
        if check_file_exists(path, desc):
            passed += 1
    
    # Validate JSON files
    print("\n📄 JSON VALIDATION")
    print("-" * 30)
    
    json_files = [
        ("frontend/admin-portal/package.json", "Package.json"),
        ("frontend/admin-portal/tsconfig.json", "TypeScript config"),
    ]
    
    for path, desc in json_files:
        total += 1
        if validate_json_file(path, desc):
            passed += 1
    
    # Docker files
    print("\n🐳 DOCKER FILES")
    print("-" * 30)
    
    docker_files = [
        ("docker-compose.yml", "Development Docker Compose"),
        ("docker-compose.prod.yml", "Production Docker Compose"),
        (".env.prod.example", "Production environment template"),
    ]
    
    for path, desc in docker_files:
        total += 1
        if check_file_exists(path, desc):
            passed += 1
    
    # Documentation files
    print("\n📚 DOCUMENTATION")
    print("-" * 30)
    
    doc_files = [
        ("README.md", "Project README"),
        ("PROJECT_COMPLETION_SUMMARY.md", "Completion summary"),
        ("TESTING_GUIDE.md", "Testing guide"),
        ("DEPLOYMENT_CHECKLIST.md", "Deployment checklist"),
        ("docs/API_DOCUMENTATION.md", "API documentation"),
        ("docs/USER_GUIDE.md", "User guide"),
        ("docs/DEPLOYMENT_GUIDE.md", "Deployment guide"),
    ]
    
    for path, desc in doc_files:
        total += 1
        if check_file_exists(path, desc):
            passed += 1
    
    # Scripts
    print("\n📜 SCRIPTS")
    print("-" * 30)
    
    script_files = [
        ("scripts/deploy-production.sh", "Production deployment script"),
        ("setup-project.ps1", "Project setup script"),
    ]
    
    for path, desc in script_files:
        total += 1
        if check_file_exists(path, desc):
            passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 VALIDATION SUMMARY")
    print("=" * 60)
    
    percentage = (passed / total) * 100 if total > 0 else 0
    
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {total - passed}")
    print(f"📊 Total: {total}")
    print(f"🎯 Success Rate: {percentage:.1f}%")
    
    if percentage >= 90:
        print("\n🎉 EXCELLENT! Project structure is complete and ready for testing!")
        return 0
    elif percentage >= 75:
        print("\n✅ GOOD! Project structure is mostly complete with minor issues.")
        return 0
    elif percentage >= 50:
        print("\n⚠️ WARNING! Project has significant missing components.")
        return 1
    else:
        print("\n❌ ERROR! Project structure is incomplete.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
