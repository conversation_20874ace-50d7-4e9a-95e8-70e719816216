"""
Audit logging tests
"""

import pytest
from fastapi.testclient import TestClient

from app.core.config import settings


class TestAuditLogging:
    """Test audit logging endpoints."""

    def test_get_audit_logs(self, client: TestClient, admin_auth_headers):
        """Test getting audit logs."""
        response = client.get(
            f"{settings.API_V1_STR}/audit/",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_get_audit_statistics(self, client: TestClient, admin_auth_headers):
        """Test getting audit statistics."""
        response = client.get(
            f"{settings.API_V1_STR}/audit/stats",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert "total_logs" in data
        assert "recent_activity" in data

    def test_get_security_report(self, client: TestClient, admin_auth_headers):
        """Test getting security report."""
        response = client.get(
            f"{settings.API_V1_STR}/audit/security-report",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert "failed_logins" in data
        assert "suspicious_activities" in data

    def test_export_audit_logs(self, client: TestClient, admin_auth_headers):
        """Test exporting audit logs."""
        export_data = {
            "format": "csv",
            "start_date": "2024-01-01",
            "end_date": "2024-12-31"
        }
        response = client.post(
            f"{settings.API_V1_STR}/audit/export",
            json=export_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 200

    def test_get_user_activity_report(self, client: TestClient, admin_auth_headers, test_user):
        """Test getting user activity report."""
        response = client.get(
            f"{settings.API_V1_STR}/audit/user/{test_user.id}/report",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert "user_id" in data
        assert "activities" in data

    def test_filter_audit_logs_by_action(self, client: TestClient, admin_auth_headers):
        """Test filtering audit logs by action."""
        response = client.get(
            f"{settings.API_V1_STR}/audit/?action=login",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_filter_audit_logs_by_user(self, client: TestClient, admin_auth_headers, test_user):
        """Test filtering audit logs by user."""
        response = client.get(
            f"{settings.API_V1_STR}/audit/?user_id={test_user.id}",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_filter_audit_logs_by_date_range(self, client: TestClient, admin_auth_headers):
        """Test filtering audit logs by date range."""
        response = client.get(
            f"{settings.API_V1_STR}/audit/?start_date=2024-01-01&end_date=2024-12-31",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_unauthorized_audit_access(self, client: TestClient, auth_headers):
        """Test unauthorized access to audit endpoints."""
        response = client.get(
            f"{settings.API_V1_STR}/audit/",
            headers=auth_headers
        )
        # Should return 403 if user doesn't have audit access
        assert response.status_code in [403, 200]  # Depends on implementation


class TestAuditLogCreation:
    """Test audit log creation during operations."""

    def test_login_creates_audit_log(self, client: TestClient, test_user, admin_auth_headers):
        """Test that login creates an audit log entry."""
        # Perform login
        client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": "testpassword"
            }
        )

        # Check if audit log was created
        response = client.get(
            f"{settings.API_V1_STR}/audit/?action=login&user_id={test_user.id}",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        # Note: This test assumes audit logging is implemented

    def test_failed_login_creates_audit_log(self, client: TestClient, test_user, admin_auth_headers):
        """Test that failed login creates an audit log entry."""
        # Perform failed login
        client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": "wrongpassword"
            }
        )

        # Check if audit log was created
        response = client.get(
            f"{settings.API_V1_STR}/audit/?action=failed_login",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        # Note: This test assumes audit logging is implemented

    def test_user_creation_creates_audit_log(self, client: TestClient, admin_auth_headers):
        """Test that user creation creates an audit log entry."""
        # Create a user
        user_data = {
            "email": "<EMAIL>",
            "username": "audituser",
            "full_name": "Audit Test User",
            "password": "password123"
        }
        client.post(
            f"{settings.API_V1_STR}/users/",
            json=user_data,
            headers=admin_auth_headers
        )

        # Check if audit log was created
        response = client.get(
            f"{settings.API_V1_STR}/audit/?action=create_user",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        # Note: This test assumes audit logging is implemented
