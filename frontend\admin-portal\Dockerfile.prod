# Multi-stage production Dockerfile for Admin Portal
ARG NODE_VERSION=20

# Dependencies stage
FROM node:${NODE_VERSION}-alpine AS deps
RUN apk add --no-cache libc6-compat

WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./

# Install dependencies
RUN npm ci --only=production --ignore-scripts

# Builder stage
FROM node:${NODE_VERSION}-alpine AS builder

WORKDIR /app

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules

# Copy source code
COPY . .

# Build arguments
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_APP_NAME
ARG NEXT_PUBLIC_ENVIRONMENT=production

# Set environment variables for build
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_APP_NAME=${NEXT_PUBLIC_APP_NAME}
ENV NEXT_PUBLIC_ENVIRONMENT=${NEXT_PUBLIC_ENVIRONMENT}
ENV NODE_ENV=production

# Install all dependencies (including dev dependencies for build)
RUN npm ci --ignore-scripts

# Build the application
RUN npm run build

# Production stage
FROM node:${NODE_VERSION}-alpine AS runner

WORKDIR /app

# Set environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Create non-root user
RUN addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json

# Copy built Next.js application
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Create health check endpoint
COPY --chown=nextjs:nodejs <<EOF /app/healthcheck.js
const http = require('http');

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/health',
  method: 'GET',
  timeout: 5000
};

const req = http.request(options, (res) => {
  if (res.statusCode === 200) {
    console.log('Health check passed');
    process.exit(0);
  } else {
    console.log(\`Health check failed with status \${res.statusCode}\`);
    process.exit(1);
  }
});

req.on('error', (err) => {
  console.log(\`Health check failed: \${err.message}\`);
  process.exit(1);
});

req.on('timeout', () => {
  console.log('Health check timed out');
  req.destroy();
  process.exit(1);
});

req.end();
EOF

# Create health check API endpoint
RUN mkdir -p pages/api
COPY --chown=nextjs:nodejs <<EOF /app/pages/api/health.js
export default function handler(req, res) {
  res.status(200).json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    service: 'admin-portal'
  });
}
EOF

# Create startup script
COPY --chown=nextjs:nodejs <<EOF /app/start.sh
#!/bin/sh
set -e

echo "Starting Innovative Centre Admin Portal..."

# Wait for backend service
echo "Waiting for backend service..."
while ! nc -z \${BACKEND_HOST:-admin-service} \${BACKEND_PORT:-8000}; do
  echo "Backend is unavailable - sleeping"
  sleep 2
done
echo "Backend is up!"

# Start the application
echo "Starting Next.js application..."
exec node server.js
EOF

RUN chmod +x /app/start.sh

# Install netcat for health checks
RUN apk add --no-cache netcat-openbsd

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD node /app/healthcheck.js

# Set hostname
ENV HOSTNAME="0.0.0.0"
ENV PORT=3000

# Default command
CMD ["/app/start.sh"]
