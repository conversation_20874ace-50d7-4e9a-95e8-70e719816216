# 📚 Innovative Centre Platform - API Documentation

## 🌐 Base Information

- **Base URL**: `http://localhost:8000`
- **API Version**: `v1`
- **API Prefix**: `/api/v1`
- **Documentation**: `http://localhost:8000/docs` (Swagger UI)
- **Alternative Docs**: `http://localhost:8000/redoc` (ReDoc)

## 🔐 Authentication

All protected endpoints require a Bearer token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

### Login Endpoint

```http
POST /api/v1/auth/login
Content-Type: application/x-www-form-urlencoded

username=<EMAIL>&password=changeme
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

## 👥 User Management

### Get Current User
```http
GET /api/v1/users/me
Authorization: Bearer <token>
```

### List Users (Admin Only)
```http
GET /api/v1/users/
Authorization: Bearer <token>
```

### Create User
```http
POST /api/v1/users/
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "username": "newuser",
  "full_name": "New User",
  "password": "securepassword"
}
```

### Update User
```http
PATCH /api/v1/users/{user_id}
Authorization: Bearer <token>
Content-Type: application/json

{
  "full_name": "Updated Name",
  "is_active": true
}
```

### Assign Role to User
```http
POST /api/v1/users/{user_id}/assign-role
Authorization: Bearer <token>
Content-Type: application/json

{
  "role_id": 1
}
```

## 🎭 Role Management

### List Roles
```http
GET /api/v1/roles/
Authorization: Bearer <token>
```

### Create Role
```http
POST /api/v1/roles/
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "teacher",
  "description": "Teacher role with course management permissions",
  "permissions": ["read:courses", "write:courses", "read:students"]
}
```

### Add Permission to Role
```http
POST /api/v1/roles/{role_id}/permissions/{permission}
Authorization: Bearer <token>
```

## 💳 Payment Management

### List Payments
```http
GET /api/v1/payments/
Authorization: Bearer <token>

# Optional query parameters:
# ?status=pending&start_date=2024-01-01&end_date=2024-12-31
# ?student_name=John&payment_method=card
```

### Create Payment
```http
POST /api/v1/payments/
Authorization: Bearer <token>
Content-Type: application/json

{
  "student_name": "John Doe",
  "student_email": "<EMAIL>",
  "amount": 150.00,
  "payment_method": "card",
  "description": "Course enrollment fee",
  "course_name": "Python Programming"
}
```

### Process Payment
```http
POST /api/v1/payments/{payment_id}/process
Authorization: Bearer <token>
```

### Refund Payment
```http
POST /api/v1/payments/{payment_id}/refund
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 75.00,
  "reason": "Partial refund requested by student"
}
```

### Payment Statistics
```http
GET /api/v1/payments/stats/summary
Authorization: Bearer <token>
```

## 💰 Financial Management

### List Financial Records
```http
GET /api/v1/financial/
Authorization: Bearer <token>

# Optional filters:
# ?type=income&category=course_fees&status=approved
# ?start_date=2024-01-01&end_date=2024-12-31
```

### Create Financial Record
```http
POST /api/v1/financial/
Authorization: Bearer <token>
Content-Type: application/json

{
  "type": "income",
  "category": "course_fees",
  "amount": 500.00,
  "description": "Course enrollment fees for Python course",
  "reference_number": "REF001"
}
```

### Approve Financial Record
```http
POST /api/v1/financial/{record_id}/approve
Authorization: Bearer <token>
```

### Financial Dashboard Data
```http
GET /api/v1/financial/dashboard/data
Authorization: Bearer <token>
```

### Financial Statistics
```http
GET /api/v1/financial/stats/summary
Authorization: Bearer <token>
```

## ⚙️ System Configuration

### List Configurations
```http
GET /api/v1/system-config/
Authorization: Bearer <token>

# Optional filters:
# ?category=general&search=email
```

### Update Configuration Value
```http
PATCH /api/v1/system-config/{config_key}/value
Authorization: Bearer <token>
Content-Type: application/json

{
  "value": "new_value"
}
```

### Bulk Update Configurations
```http
POST /api/v1/system-config/bulk-update
Authorization: Bearer <token>
Content-Type: application/json

{
  "updates": [
    {"key": "email_host", "value": "smtp.gmail.com"},
    {"key": "email_port", "value": "587"}
  ]
}
```

### List Configuration Categories
```http
GET /api/v1/system-config/categories/list
Authorization: Bearer <token>
```

## 📋 Audit Logging

### List Audit Logs
```http
GET /api/v1/audit/
Authorization: Bearer <token>

# Optional filters:
# ?action=login&user_id=1&start_date=2024-01-01
# ?ip_address=***********&resource_type=user
```

### Audit Statistics
```http
GET /api/v1/audit/stats
Authorization: Bearer <token>
```

### Security Report
```http
GET /api/v1/audit/security-report
Authorization: Bearer <token>
```

### Export Audit Logs
```http
POST /api/v1/audit/export
Authorization: Bearer <token>
Content-Type: application/json

{
  "format": "csv",
  "start_date": "2024-01-01",
  "end_date": "2024-12-31",
  "filters": {
    "action": "login",
    "user_id": 1
  }
}
```

### User Activity Report
```http
GET /api/v1/audit/user/{user_id}/report
Authorization: Bearer <token>
```

## 📊 Response Formats

### Success Response
```json
{
  "id": 1,
  "created_at": "2024-01-01T12:00:00Z",
  "updated_at": "2024-01-01T12:00:00Z",
  "data": "..."
}
```

### Error Response
```json
{
  "detail": "Error message",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Paginated Response
```json
{
  "items": [...],
  "pagination": {
    "page": 1,
    "page_size": 50,
    "total": 100,
    "pages": 2,
    "has_next": true,
    "has_prev": false
  }
}
```

## 🔒 Permissions

### Available Permissions

**User Management:**
- `read:users` - View user information
- `write:users` - Create and update users
- `delete:users` - Delete users
- `manage:roles` - Assign roles to users

**Payment Management:**
- `read:payments` - View payment records
- `write:payments` - Create payment records
- `process:payments` - Process payments
- `refund:payments` - Process refunds

**Financial Management:**
- `read:financial` - View financial records
- `write:financial` - Create financial records
- `approve:financial` - Approve financial records
- `manage:financial` - Full financial management

**System Administration:**
- `read:system_config` - View system configuration
- `write:system_config` - Modify system configuration
- `read:audit` - View audit logs
- `export:audit` - Export audit logs

**Role-Based Access:**
- `super_admin` - Full system access
- `admin` - Administrative access
- `manager` - General management operations
- `financial_manager` - Financial operations
- `accountant` - Accounting operations
- `cashier` - Payment processing
- `reception` - Reception operations

## 🚨 Rate Limiting

- **Default Limit**: 100 requests per hour per IP
- **Authentication Endpoints**: 10 requests per 15 minutes per IP
- **Headers**: 
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset time (Unix timestamp)

## 🔧 Error Codes

| Code | Description |
|------|-------------|
| `VALIDATION_ERROR` | Input validation failed |
| `AUTHENTICATION_ERROR` | Authentication required or failed |
| `AUTHORIZATION_ERROR` | Insufficient permissions |
| `NOT_FOUND` | Resource not found |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `INTERNAL_ERROR` | Server error |

## 📝 Examples

### Complete Payment Flow
```bash
# 1. Login
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=changeme"

# 2. Create Payment
curl -X POST "http://localhost:8000/api/v1/payments/" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "student_name": "John Doe",
    "student_email": "<EMAIL>",
    "amount": 150.00,
    "payment_method": "card",
    "description": "Course fee"
  }'

# 3. Process Payment
curl -X POST "http://localhost:8000/api/v1/payments/1/process" \
  -H "Authorization: Bearer <token>"
```
