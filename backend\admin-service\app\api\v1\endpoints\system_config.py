"""
System configuration endpoints
"""

from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.db.session import get_db
from app.models.user import User
from app.schemas.system_config import (
    SystemConfigCreate, SystemConfigResponse, SystemConfigUpdate,
    SystemConfigFilter, ConfigValueUpdate, BulkConfigUpdate
)
from app.services.user_service import user_service
from app.services.system_config_service import system_config_service

router = APIRouter()


@router.get("/", response_model=List[SystemConfigResponse])
async def get_system_configs(
    db: Session = Depends(get_db),
    skip: int = 0,
    limit: int = 100,
    category: str = Query(None, description="Filter by category"),
    is_active: bool = Query(None, description="Filter by active status"),
    search: str = Query(None, description="Search in key, name, or description"),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Retrieve system configurations with optional filters
    """
    filters = SystemConfigFilter(
        category=category,
        is_active=is_active,
        search=search
    )
    
    configs = system_config_service.get_filtered(
        db, filters=filters, skip=skip, limit=limit
    )
    
    # Add typed values to response
    for config in configs:
        config.typed_value = config.get_typed_value()
    
    return configs


@router.post("/", response_model=SystemConfigResponse)
async def create_system_config(
    *,
    db: Session = Depends(get_db),
    config_in: SystemConfigCreate,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Create new system configuration (superuser only)
    """
    try:
        config = system_config_service.create(db, obj_in=config_in, created_by=current_user)
        config.typed_value = config.get_typed_value()
        return config
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/{config_key}", response_model=SystemConfigResponse)
async def get_system_config(
    config_key: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get system configuration by key
    """
    config = system_config_service.get_by_key(db, key=config_key)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Configuration not found"
        )
    
    config.typed_value = config.get_typed_value()
    return config


@router.put("/{config_key}", response_model=SystemConfigResponse)
async def update_system_config(
    *,
    db: Session = Depends(get_db),
    config_key: str,
    config_in: SystemConfigUpdate,
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Update system configuration
    """
    config = system_config_service.get_by_key(db, key=config_key)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Configuration not found"
        )
    
    # Check if it's a system config and user is superuser
    if config.is_system and not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only superusers can modify system configurations"
        )
    
    config = system_config_service.update(db, db_obj=config, obj_in=config_in)
    config.typed_value = config.get_typed_value()
    return config


@router.patch("/{config_key}/value", response_model=SystemConfigResponse)
async def update_config_value(
    *,
    db: Session = Depends(get_db),
    config_key: str,
    value_update: ConfigValueUpdate,
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Update only the value of a configuration
    """
    try:
        config = system_config_service.update_config_value(
            db, key=config_key, value=value_update.value, updated_by=current_user
        )
        config.typed_value = config.get_typed_value()
        return config
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{config_key}/reset", response_model=SystemConfigResponse)
async def reset_config_to_default(
    *,
    db: Session = Depends(get_db),
    config_key: str,
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Reset configuration to default value
    """
    try:
        config = system_config_service.reset_to_default(
            db, key=config_key, updated_by=current_user
        )
        config.typed_value = config.get_typed_value()
        return config
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/bulk-update")
async def bulk_update_configs(
    *,
    db: Session = Depends(get_db),
    bulk_update: BulkConfigUpdate,
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Bulk update multiple configurations
    """
    results = system_config_service.bulk_update(
        db, updates=bulk_update, updated_by=current_user
    )
    return results


@router.get("/categories/list")
async def get_config_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get all configuration categories with counts
    """
    categories = system_config_service.get_categories(db)
    return {"categories": categories}


@router.get("/category/{category_name}")
async def get_configs_by_category(
    category_name: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_user)
) -> Any:
    """
    Get all configurations in a specific category
    """
    configs = system_config_service.get_by_category(db, category=category_name)
    
    # Add typed values to response
    for config in configs:
        config.typed_value = config.get_typed_value()
    
    return {"category": category_name, "configs": configs}


@router.post("/initialize-defaults")
async def initialize_default_configs(
    db: Session = Depends(get_db),
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Initialize default system configurations (superuser only)
    """
    created_count = system_config_service.initialize_default_configs(
        db, created_by=current_user
    )
    return {
        "message": f"Initialized {created_count} default configurations",
        "created_count": created_count
    }


@router.delete("/{config_key}")
async def delete_system_config(
    *,
    db: Session = Depends(get_db),
    config_key: str,
    current_user: User = Depends(user_service.get_current_active_superuser)
) -> Any:
    """
    Delete system configuration (superuser only)
    """
    config = system_config_service.get_by_key(db, key=config_key)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Configuration not found"
        )
    
    if config.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete system configurations"
        )
    
    system_config_service.remove(db, id=config.id)
    return {"message": "Configuration deleted successfully"}
