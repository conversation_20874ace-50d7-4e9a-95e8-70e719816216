"""
Financial management tests
"""

import pytest
from fastapi.testclient import Test<PERSON>lient
from decimal import Decimal

from app.core.config import settings


class TestFinancialManagement:
    """Test financial management endpoints."""

    def test_create_financial_record(self, client: TestClient, admin_auth_headers):
        """Test creating a new financial record."""
        record_data = {
            "type": "income",
            "category": "course_fees",
            "amount": 500.00,
            "description": "Course enrollment fees",
            "reference_number": "REF001"
        }
        response = client.post(
            f"{settings.API_V1_STR}/financial/",
            json=record_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 201
        data = response.json()
        assert data["type"] == record_data["type"]
        assert float(data["amount"]) == record_data["amount"]
        assert data["status"] == "pending"

    def test_create_expense_record(self, client: TestClient, admin_auth_headers):
        """Test creating an expense record."""
        record_data = {
            "type": "expense",
            "category": "utilities",
            "amount": 200.00,
            "description": "Monthly electricity bill",
            "reference_number": "UTIL001"
        }
        response = client.post(
            f"{settings.API_V1_STR}/financial/",
            json=record_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 201
        data = response.json()
        assert data["type"] == "expense"
        assert float(data["amount"]) == 200.00

    def test_get_financial_records(self, client: TestClient, admin_auth_headers):
        """Test getting list of financial records."""
        response = client.get(
            f"{settings.API_V1_STR}/financial/",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_approve_financial_record(self, client: TestClient, admin_auth_headers):
        """Test approving a financial record."""
        # Create a record first
        record_data = {
            "type": "income",
            "category": "consulting",
            "amount": 1000.00,
            "description": "Consulting services",
            "reference_number": "CONS001"
        }
        create_response = client.post(
            f"{settings.API_V1_STR}/financial/",
            json=record_data,
            headers=admin_auth_headers
        )
        record_id = create_response.json()["id"]

        # Approve the record
        response = client.post(
            f"{settings.API_V1_STR}/financial/{record_id}/approve",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "approved"

    def test_get_financial_dashboard_data(self, client: TestClient, admin_auth_headers):
        """Test getting financial dashboard data."""
        response = client.get(
            f"{settings.API_V1_STR}/financial/dashboard/data",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert "total_income" in data
        assert "total_expenses" in data
        assert "net_profit" in data
        assert "monthly_trends" in data

    def test_get_financial_statistics(self, client: TestClient, admin_auth_headers):
        """Test getting financial statistics."""
        response = client.get(
            f"{settings.API_V1_STR}/financial/stats/summary",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert "total_records" in data
        assert "income_summary" in data
        assert "expense_summary" in data

    def test_filter_by_type(self, client: TestClient, admin_auth_headers):
        """Test filtering financial records by type."""
        response = client.get(
            f"{settings.API_V1_STR}/financial/?type=income",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_filter_by_category(self, client: TestClient, admin_auth_headers):
        """Test filtering financial records by category."""
        response = client.get(
            f"{settings.API_V1_STR}/financial/?category=course_fees",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_filter_by_date_range(self, client: TestClient, admin_auth_headers):
        """Test filtering financial records by date range."""
        response = client.get(
            f"{settings.API_V1_STR}/financial/?start_date=2024-01-01&end_date=2024-12-31",
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_update_financial_record(self, client: TestClient, admin_auth_headers):
        """Test updating a financial record."""
        # Create a record first
        record_data = {
            "type": "expense",
            "category": "office_supplies",
            "amount": 50.00,
            "description": "Office supplies purchase"
        }
        create_response = client.post(
            f"{settings.API_V1_STR}/financial/",
            json=record_data,
            headers=admin_auth_headers
        )
        record_id = create_response.json()["id"]

        # Update the record
        update_data = {
            "amount": 75.00,
            "description": "Updated office supplies purchase"
        }
        response = client.patch(
            f"{settings.API_V1_STR}/financial/{record_id}",
            json=update_data,
            headers=admin_auth_headers
        )
        assert response.status_code == 200
        data = response.json()
        assert float(data["amount"]) == 75.00

    def test_delete_financial_record(self, client: TestClient, admin_auth_headers):
        """Test deleting a financial record."""
        # Create a record first
        record_data = {
            "type": "expense",
            "category": "miscellaneous",
            "amount": 25.00,
            "description": "Test expense"
        }
        create_response = client.post(
            f"{settings.API_V1_STR}/financial/",
            json=record_data,
            headers=admin_auth_headers
        )
        record_id = create_response.json()["id"]

        # Delete the record
        response = client.delete(
            f"{settings.API_V1_STR}/financial/{record_id}",
            headers=admin_auth_headers
        )
        assert response.status_code == 200

        # Verify deletion
        get_response = client.get(
            f"{settings.API_V1_STR}/financial/{record_id}",
            headers=admin_auth_headers
        )
        assert get_response.status_code == 404
